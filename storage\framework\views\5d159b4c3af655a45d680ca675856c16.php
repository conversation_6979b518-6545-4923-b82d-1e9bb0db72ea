<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    
    <title>EasyLink SDK Test Dashboard</title>
    
    <?php echo app('Illuminate\Foundation\Vite')(['resources/css/app.css', 'resources/js/app.js']); ?>
</head>
<body>
    <div id="easylink-dashboard" class="container">
        <!-- Header -->
        <div class="header">
            <h1><i class="fas fa-fingerprint"></i> EasyLink SDK</h1>
            <p>Test Dashboard & Configuration Manager</p>
        </div>

        <!-- Status Overview -->
        <div class="card" style="margin-bottom: 2rem;">
            <div class="card-header">
                <div class="card-icon primary">
                    <i class="fas fa-info-circle"></i>
                </div>
                <div>
                    <h3 class="card-title">System Status</h3>
                </div>
            </div>
            
            <div style="display: flex; gap: 1rem; align-items: center; flex-wrap: wrap;">
                <div id="connection-status" class="status-indicator status-disconnected">
                    <i class="fas fa-times-circle"></i> Disconnected
                </div>
                
                <label style="display: flex; align-items: center; gap: 0.5rem; margin-left: auto;">
                    <input type="checkbox" id="auto-refresh" style="margin: 0;">
                    <span>Auto-refresh connectivity (30s)</span>
                </label>
            </div>
        </div>

        <!-- Dashboard Grid -->
        <div class="dashboard-grid">
            <!-- Configuration Card -->
            <div class="card">
                <div class="card-header">
                    <div class="card-icon primary">
                        <i class="fas fa-cog"></i>
                    </div>
                    <div>
                        <h3 class="card-title">Configuration</h3>
                    </div>
                </div>
                
                <div id="config-display">
                    <div style="text-align: center; color: var(--gray-500); padding: 2rem;">
                        <i class="fas fa-spinner fa-spin"></i> Loading configuration...
                    </div>
                </div>
                
                <div style="margin-top: 1.5rem;">
                    <button id="test-config" class="btn btn-primary">
                        <i class="fas fa-sync"></i> Refresh Config
                    </button>
                </div>
                
                <div id="config-result"></div>
            </div>

            <!-- Connectivity Test Card -->
            <div class="card">
                <div class="card-header">
                    <div class="card-icon warning">
                        <i class="fas fa-wifi"></i>
                    </div>
                    <div>
                        <h3 class="card-title">Device Connectivity</h3>
                    </div>
                </div>
                
                <p style="color: var(--gray-600); margin-bottom: 1.5rem;">
                    Test connection to the EasyLink device and verify network accessibility.
                </p>
                
                <button id="test-connectivity" class="btn btn-warning">
                    <i class="fas fa-satellite-dish"></i> Test Connection
                </button>
                
                <div id="connectivity-result"></div>
            </div>

            <!-- Device Information Card -->
            <div class="card">
                <div class="card-header">
                    <div class="card-icon success">
                        <i class="fas fa-microchip"></i>
                    </div>
                    <div>
                        <h3 class="card-title">Device Information</h3>
                    </div>
                </div>
                
                <p style="color: var(--gray-600); margin-bottom: 1.5rem;">
                    Retrieve detailed information about the connected EasyLink device.
                </p>
                
                <button id="test-device" class="btn btn-success">
                    <i class="fas fa-info"></i> Get Device Info
                </button>
                
                <div id="device-result"></div>
            </div>

            <!-- Attendance Logs Card -->
            <div class="card">
                <div class="card-header">
                    <div class="card-icon error">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div>
                        <h3 class="card-title">Attendance Logs</h3>
                    </div>
                </div>
                
                <p style="color: var(--gray-600); margin-bottom: 1.5rem;">
                    Fetch the latest attendance scan logs from the device.
                </p>
                
                <button id="test-attendance" class="btn btn-error">
                    <i class="fas fa-download"></i> Get Attendance Logs
                </button>
                
                <div id="attendance-result"></div>
            </div>
        </div>

        <!-- API Endpoints Reference -->
        <div class="card">
            <div class="card-header">
                <div class="card-icon primary">
                    <i class="fas fa-code"></i>
                </div>
                <div>
                    <h3 class="card-title">API Endpoints Reference</h3>
                </div>
            </div>
            
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 1rem;">
                <div>
                    <h4 style="color: var(--gray-700); margin-bottom: 0.5rem;">Configuration</h4>
                    <code style="background: var(--gray-100); padding: 0.5rem; border-radius: 4px; display: block; font-size: 0.875rem;">
                        GET /easylink/configuration
                    </code>
                </div>
                
                <div>
                    <h4 style="color: var(--gray-700); margin-bottom: 0.5rem;">Connectivity Check</h4>
                    <code style="background: var(--gray-100); padding: 0.5rem; border-radius: 4px; display: block; font-size: 0.875rem;">
                        GET /easylink/check-connectivity
                    </code>
                </div>
                
                <div>
                    <h4 style="color: var(--gray-700); margin-bottom: 0.5rem;">Device Information</h4>
                    <code style="background: var(--gray-100); padding: 0.5rem; border-radius: 4px; display: block; font-size: 0.875rem;">
                        GET /easylink/device-info
                    </code>
                </div>
                
                <div>
                    <h4 style="color: var(--gray-700); margin-bottom: 0.5rem;">Attendance Logs</h4>
                    <code style="background: var(--gray-100); padding: 0.5rem; border-radius: 4px; display: block; font-size: 0.875rem;">
                        GET /easylink/attendance-logs
                    </code>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <div style="text-align: center; margin-top: 3rem; color: white; opacity: 0.8;">
            <p>EasyLink SDK v1.0 | Laravel <?php echo e(Illuminate\Foundation\Application::VERSION); ?> | PHP <?php echo e(PHP_VERSION); ?></p>
        </div>
    </div>
</body>
</html>
<?php /**PATH C:\laragon\www\larattendance\resources\views/easylink/dashboard.blade.php ENDPATH**/ ?>