# EasyLink Yuswa Module

A modular Laravel implementation of the EasyLink SDK by Yuswa Arba for Fingerspot EasyLink device integration.

## Overview

This module provides a complete, modular implementation of the EasyLink SDK with:
- **Modular Architecture**: Self-contained module structure
- **Modern Dashboard**: Beautiful web interface for testing and monitoring
- **RESTful API**: Clean API endpoints for integration
- **Real-time Testing**: Live connectivity and device testing
- **Configuration Management**: Environment-based configuration
- **Command Line Tools**: Artisan commands for testing and management

## Installation & Setup

### 1. Module Structure
The module is located in `modules/EasyLinkYuswa/` with the following structure:
```
modules/EasyLinkYuswa/
├── Config/
│   └── config.php              # Module configuration
├── Console/
│   └── TestEasyLinkCommand.php # Artisan command
├── Http/
│   └── Controllers/            # Module controllers
├── Providers/
│   ├── EasyLinkYuswaServiceProvider.php
│   └── RouteServiceProvider.php
├── Resources/
│   └── views/                  # Module views
├── Routes/
│   ├── web.php                 # Web routes
│   └── api.php                 # API routes
├── Services/
│   └── EasyLinkService.php     # Business logic
├── composer.json               # Module dependencies
└── module.json                 # Module metadata
```

### 2. Dependencies
The module uses the parent application's composer dependencies:
- **Guzzle HTTP Client**: Already included in the main composer.json
- **Laravel Framework**: Uses the application's Laravel installation
- **No separate composer.json**: All dependencies managed at the application level

### 3. Configuration
Configure the module through environment variables in your **root `.env` file**:

```env
# EasyLink Yuswa Module Configuration
EASYLINK_YUSWA_SDK_HOST=http://**************:5005
EASYLINK_YUSWA_SDK_SN=66208023321907
EASYLINK_YUSWA_SERVER_PORT=7005

# Database Configuration
EASYLINK_YUSWA_DB_HOST=localhost
EASYLINK_YUSWA_DB_DATABASE=fin_pro_test
EASYLINK_YUSWA_DB_USERNAME=root
EASYLINK_YUSWA_DB_PASSWORD=
EASYLINK_YUSWA_DB_PORT=3306

# Module Settings
EASYLINK_YUSWA_MODULE_ENABLED=true
EASYLINK_YUSWA_DEBUG=false
EASYLINK_YUSWA_TIMEOUT=30
EASYLINK_YUSWA_AUTO_REFRESH=true
EASYLINK_YUSWA_REFRESH_INTERVAL=30

# Logging Settings
EASYLINK_YUSWA_LOGGING_ENABLED=true
EASYLINK_YUSWA_LOG_LEVEL=info
EASYLINK_YUSWA_LOG_CHANNEL=single
```

### 4. Service Provider Registration
The module service provider is already registered in `config/app.php`:
```php
'providers' => [
    // Other providers...
    Modules\EasyLinkYuswa\Providers\EasyLinkYuswaServiceProvider::class,
],
```

**Note:** The module uses the parent application's autoloader and dependencies. No separate composer installation is required.

## Usage

### Web Interface

#### Dashboard
Access the main dashboard at:
```
http://larattendance.me/easylinkyuswa
```

Features:
- **Real-time Status**: Live connection status indicator
- **Configuration Display**: Current module settings
- **Device Testing**: Test device connectivity and information
- **Attendance Logs**: Retrieve and display attendance data
- **Auto-refresh**: Automatic connectivity monitoring

#### Additional Pages
- **Module Info**: `/easylinkyuswa/info` - Module details and documentation
- **Settings**: `/easylinkyuswa/settings` - Configuration management

### API Endpoints

#### Device Management
```
GET /api/easylinkyuswa/device/info          # Get device information
GET /api/easylinkyuswa/device/connectivity  # Check device connectivity
GET /api/easylinkyuswa/device/status        # Get device status
GET /api/easylinkyuswa/device/test          # Test device functionality
```

#### Attendance Management
```
GET /api/easylinkyuswa/attendance/logs      # Get attendance logs
GET /api/easylinkyuswa/attendance/test      # Test attendance functionality
POST /api/easylinkyuswa/attendance/sync     # Sync attendance data
```

#### Configuration
```
GET /api/easylinkyuswa/config               # Get module configuration
GET /api/easylinkyuswa/config/stats         # Get module statistics
```

#### Health Check
```
GET /api/easylinkyuswa/health               # Module health check
GET /api/easylinkyuswa/info                 # Module information
```

### Programmatic Usage

#### Using the Service
```php
use Modules\EasyLinkYuswa\Services\EasyLinkService;

$service = app(EasyLinkService::class);

// Get device information
$deviceInfo = $service->getDeviceInfo();

// Get attendance logs
$logs = $service->getFormattedAttendanceLogs();

// Check connectivity
$status = $service->checkDeviceConnectivity();

// Get configuration
$config = $service->getConfiguration();
```

#### Using Service Container
```php
// Resolve from container
$service = app('easylinkyuswa.service');
$deviceInfo = $service->getDeviceInfo();
```

## Features

### Dashboard Features
- **Real-time Monitoring**: Live device status updates
- **Interactive Testing**: One-click device and attendance testing
- **Configuration Display**: Visual representation of current settings
- **Attendance Preview**: Recent attendance logs display
- **Quick Actions**: Sync, test, and cache management buttons
- **Responsive Design**: Mobile-friendly interface

### API Features
- **RESTful Design**: Clean, consistent API endpoints
- **Error Handling**: Comprehensive error responses
- **Rate Limiting**: Built-in API rate limiting
- **JSON Responses**: Standardized JSON response format
- **Health Checks**: System health monitoring endpoints

### Service Features
- **HTTP Client**: Guzzle-based HTTP communication
- **Error Logging**: Comprehensive error logging
- **Configuration Management**: Environment-based configuration
- **Data Formatting**: Standardized data formatting
- **Retry Logic**: Built-in retry mechanisms

## Command Line Interface

### Test Command
Test the module functionality using Artisan commands:

```bash
# Test all functionality
php artisan easylinkyuswa:test --all

# Test device connectivity only
php artisan easylinkyuswa:test --device

# Test attendance logs only
php artisan easylinkyuswa:test --attendance
```

## Configuration Options

### Device Settings
- `sdk_host`: EasyLink device host URL
- `sdk_sn`: Device serial number
- `server_port`: Device server port
- `timeout`: Connection timeout in seconds

### Module Settings
- `enabled`: Enable/disable module
- `debug`: Debug mode toggle
- `auto_refresh`: Auto-refresh dashboard
- `refresh_interval`: Refresh interval in seconds

### API Settings
- `rate_limit`: API rate limit (requests per minute)
- `middleware`: API middleware configuration

### Logging Settings
- `logging.enabled`: Enable/disable logging
- `logging.level`: Log level (debug, info, warning, error)
- `logging.channel`: Log channel configuration

## Troubleshooting

### Common Issues

1. **Connection Failed**
   - Check device IP address and port
   - Verify network connectivity
   - Ensure device is powered on

2. **No Attendance Data**
   - Verify device has attendance records
   - Check device time synchronization
   - Ensure proper device configuration

3. **Module Not Loading**
   - Verify service provider registration
   - Check module configuration
   - Clear Laravel cache

### Debug Mode
Enable debug mode for detailed error information:
```env
EASYLINK_YUSWA_DEBUG=true
```

## Support

For support and documentation, please refer to:
- Module dashboard: `/easylinkyuswa`
- API documentation: `/api/easylinkyuswa/info`
- Laravel logs: `storage/logs/laravel.log`

## License

This module is open-sourced software licensed under the [MIT license](https://opensource.org/licenses/MIT).
