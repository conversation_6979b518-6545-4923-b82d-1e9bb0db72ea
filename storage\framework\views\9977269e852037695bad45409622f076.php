<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    <title><?php echo $__env->yieldContent('title', 'EasyLink Yuswa Dashboard'); ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <style>
        .status-online { color: #28a745; }
        .status-offline { color: #dc3545; }
        .card-header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; }
        .btn-primary { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border: none; }
        .btn-primary:hover { background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%); }
        .navbar-brand { font-weight: bold; }
        .card { box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); border: none; }
        .table th { background-color: #f8f9fa; }
        .spinner-border-sm { width: 1rem; height: 1rem; }
        .alert-dismissible .btn-close { padding: 0.375rem 0.75rem; }
        .bg-gradient-primary { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .text-gradient { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); -webkit-background-clip: text; -webkit-text-fill-color: transparent; }
    </style>
    <?php echo $__env->yieldContent('styles'); ?>
</head>
<body class="bg-light">
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-gradient-primary">
        <div class="container">
            <a class="navbar-brand" href="<?php echo e(route('easylinkyuswa.dashboard')); ?>">
                <i class="fas fa-fingerprint me-2"></i>EasyLink Yuswa
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link <?php echo e(request()->routeIs('easylinkyuswa.dashboard*') ? 'active' : ''); ?>" 
                           href="<?php echo e(route('easylinkyuswa.dashboard')); ?>">
                            <i class="fas fa-tachometer-alt me-1"></i>Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo e(request()->routeIs('easylinkyuswa.info') ? 'active' : ''); ?>" 
                           href="<?php echo e(route('easylinkyuswa.info')); ?>">
                            <i class="fas fa-info-circle me-1"></i>Module Info
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo e(request()->routeIs('easylinkyuswa.settings') ? 'active' : ''); ?>" 
                           href="<?php echo e(route('easylinkyuswa.settings')); ?>">
                            <i class="fas fa-cog me-1"></i>Settings
                        </a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <span class="navbar-text">
                            <i class="fas fa-circle status-indicator" id="connection-status"></i>
                            <span id="connection-text">Checking...</span>
                        </span>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container mt-4">
        <?php echo $__env->yieldContent('content'); ?>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-light text-center py-3 mt-5">
        <div class="container">
            <p class="mb-0">&copy; <?php echo e(date('Y')); ?> EasyLink Yuswa Module. Powered by Laravel Attendance System.</p>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    
    <!-- Global JavaScript -->
    <script>
        // CSRF Token Setup
        $.ajaxSetup({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        });

        // Connection Status Check
        function checkConnectionStatus() {
            $.get('<?php echo e(route("easylinkyuswa.api.realtime.status")); ?>')
                .done(function(response) {
                    if (response.success && response.data.connected) {
                        $('#connection-status').removeClass('status-offline').addClass('status-online');
                        $('#connection-text').text('Online (' + response.data.response_time + ')');
                    } else {
                        $('#connection-status').removeClass('status-online').addClass('status-offline');
                        $('#connection-text').text('Offline');
                    }
                })
                .fail(function() {
                    $('#connection-status').removeClass('status-online').addClass('status-offline');
                    $('#connection-text').text('Error');
                });
        }

        // Auto-refresh connection status
        $(document).ready(function() {
            checkConnectionStatus();
            setInterval(checkConnectionStatus, 30000); // Check every 30 seconds
        });

        // Utility Functions
        function showAlert(message, type = 'info') {
            const alertHtml = `
                <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            $('.container').first().prepend(alertHtml);
        }

        function formatDateTime(dateString) {
            return new Date(dateString).toLocaleString();
        }

        function formatResponseTime(responseTime) {
            return responseTime ? responseTime : 'N/A';
        }
    </script>
    
    <?php echo $__env->yieldContent('scripts'); ?>
</body>
</html>
<?php /**PATH C:\laragon\www\larattendance\modules\EasyLinkYuswa\Providers/../Resources/views/layouts/app.blade.php ENDPATH**/ ?>