# EasyLink Yuswa Module Configuration
# Add these variables to your main application's .env file (not a separate .env file)
# The module reads configuration from the root .env file

# =============================================================================
# Device Configuration
# =============================================================================

# EasyLink device host URL (IP address and port)
EASYLINK_YUSWA_SDK_HOST=http://**************:5005

# Device serial number (unique identifier for your EasyLink device)
EASYLINK_YUSWA_SDK_SN=66208023321907

# Device server port
EASYLINK_YUSWA_SERVER_PORT=7005

# =============================================================================
# Database Configuration
# =============================================================================

# Database connection settings for the EasyLink device database
EASYLINK_YUSWA_DB_HOST=localhost
EASYLINK_YUSWA_DB_DATABASE=fin_pro_test
EASYLINK_YUSWA_DB_USERNAME=root
EASYLINK_YUSWA_DB_PASSWORD=
EASYLINK_YUSWA_DB_PORT=3306

# =============================================================================
# Module Settings
# =============================================================================

# Enable or disable the module
EASYLINK_YUSWA_MODULE_ENABLED=true

# Enable debug mode for detailed error logging
EASYLINK_YUSWA_DEBUG=false

# Connection timeout in seconds
EASYLINK_YUSWA_TIMEOUT=30

# Auto-refresh dashboard data
EASYLINK_YUSWA_AUTO_REFRESH=true

# Auto-refresh interval in seconds
EASYLINK_YUSWA_REFRESH_INTERVAL=30

# =============================================================================
# Logging Configuration
# =============================================================================

# Enable or disable module logging
EASYLINK_YUSWA_LOGGING_ENABLED=true

# Log level (debug, info, warning, error)
EASYLINK_YUSWA_LOG_LEVEL=info

# Log channel (single, daily, stack, etc.)
EASYLINK_YUSWA_LOG_CHANNEL=single

# =============================================================================
# Advanced Settings
# =============================================================================

# Maximum retry attempts for failed requests
EASYLINK_YUSWA_MAX_RETRIES=3

# Retry delay in milliseconds
EASYLINK_YUSWA_RETRY_DELAY=1000

# Connection timeout in seconds
EASYLINK_YUSWA_CONNECTION_TIMEOUT=10

# Read timeout in seconds
EASYLINK_YUSWA_READ_TIMEOUT=30
