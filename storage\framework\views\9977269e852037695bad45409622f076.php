<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    <title><?php echo $__env->yieldContent('title', 'EasyLink Yuswa Dashboard'); ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Animate.css -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <style>
        :root {
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            --warning-gradient: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
            --danger-gradient: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
            --dark-gradient: linear-gradient(135deg, #434343 0%, #000000 100%);
            --card-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            --card-shadow-hover: 0 20px 40px rgba(0, 0, 0, 0.15);
            --border-radius: 15px;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        * {
            font-family: 'Inter', sans-serif;
        }

        body {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
        }

        /* Status Indicators */
        .status-online {
            color: #28a745;
            animation: pulse 2s infinite;
        }
        .status-offline {
            color: #dc3545;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        /* Navigation */
        .navbar {
            background: var(--primary-gradient) !important;
            backdrop-filter: blur(10px);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            padding: 1rem 0;
        }

        .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        .nav-link {
            font-weight: 500;
            transition: var(--transition);
            border-radius: 8px;
            margin: 0 4px;
            padding: 8px 16px !important;
        }

        .nav-link:hover, .nav-link.active {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }

        /* Cards */
        .card {
            border: none;
            border-radius: var(--border-radius);
            box-shadow: var(--card-shadow);
            transition: var(--transition);
            overflow: hidden;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: var(--card-shadow-hover);
        }

        .card-header {
            background: var(--primary-gradient);
            color: white;
            border: none;
            padding: 1.25rem;
            font-weight: 600;
        }

        .card-body {
            padding: 1.5rem;
        }

        /* Buttons */
        .btn {
            border-radius: 10px;
            font-weight: 500;
            transition: var(--transition);
            border: none;
            padding: 0.75rem 1.5rem;
        }

        .btn-primary {
            background: var(--primary-gradient);
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);
        }

        .btn-success {
            background: var(--success-gradient);
            box-shadow: 0 4px 15px rgba(79, 172, 254, 0.4);
        }

        .btn-warning {
            background: var(--warning-gradient);
            box-shadow: 0 4px 15px rgba(67, 233, 123, 0.4);
        }

        .btn-danger {
            background: var(--danger-gradient);
            box-shadow: 0 4px 15px rgba(250, 112, 154, 0.4);
        }

        .btn-outline-primary {
            border: 2px solid #667eea;
            color: #667eea;
            background: transparent;
        }

        .btn-outline-primary:hover {
            background: var(--primary-gradient);
            border-color: transparent;
            transform: translateY(-2px);
        }

        /* Stats Cards */
        .stats-card {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.7) 100%);
            border-left: 4px solid;
            transition: var(--transition);
        }

        .stats-card.online {
            border-left-color: #28a745;
        }

        .stats-card.offline {
            border-left-color: #dc3545;
        }

        .stats-card.info {
            border-left-color: #17a2b8;
        }

        .stats-card.warning {
            border-left-color: #ffc107;
        }

        /* Tables */
        .table {
            border-radius: var(--border-radius);
            overflow: hidden;
            box-shadow: var(--card-shadow);
        }

        .table th {
            background: var(--primary-gradient);
            color: white;
            border: none;
            font-weight: 600;
            padding: 1rem;
        }

        .table td {
            padding: 1rem;
            border-color: rgba(0, 0, 0, 0.05);
            vertical-align: middle;
        }

        .table tbody tr:hover {
            background: rgba(102, 126, 234, 0.05);
            transform: scale(1.01);
            transition: var(--transition);
        }

        /* Alerts */
        .alert {
            border: none;
            border-radius: var(--border-radius);
            box-shadow: var(--card-shadow);
            backdrop-filter: blur(10px);
        }

        /* Loading Animations */
        .loading-skeleton {
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            animation: loading 1.5s infinite;
        }

        @keyframes loading {
            0% { background-position: 200% 0; }
            100% { background-position: -200% 0; }
        }

        /* Progress Bars */
        .progress {
            height: 8px;
            border-radius: 10px;
            background: rgba(0, 0, 0, 0.1);
        }

        .progress-bar {
            background: var(--primary-gradient);
            border-radius: 10px;
        }

        /* Badges */
        .badge {
            border-radius: 8px;
            font-weight: 500;
            padding: 0.5rem 0.75rem;
        }

        /* Footer */
        footer {
            background: var(--dark-gradient) !important;
            margin-top: 4rem;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .card {
                margin-bottom: 1rem;
            }

            .btn {
                width: 100%;
                margin-bottom: 0.5rem;
            }
        }

        /* Custom Scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 10px;
        }

        ::-webkit-scrollbar-thumb {
            background: var(--primary-gradient);
            border-radius: 10px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: #555;
        }

        /* Text Gradients */
        .text-gradient {
            background: var(--primary-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-weight: 700;
        }

        .text-gradient-secondary {
            background: var(--secondary-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-weight: 700;
        }

        /* Icon Animations */
        .icon-spin {
            animation: spin 2s linear infinite;
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        .icon-bounce {
            animation: bounce 1s infinite;
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-10px); }
            60% { transform: translateY(-5px); }
        }
    </style>
    <?php echo $__env->yieldContent('styles'); ?>
</head>
<body class="bg-light">
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand animate__animated animate__fadeInLeft" href="<?php echo e(route('easylinkyuswa.dashboard')); ?>">
                <i class="fas fa-fingerprint me-2 icon-bounce"></i>EasyLink Yuswa
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link <?php echo e(request()->routeIs('easylinkyuswa.dashboard*') ? 'active' : ''); ?>"
                           href="<?php echo e(route('easylinkyuswa.dashboard')); ?>">
                            <i class="fas fa-tachometer-alt me-1"></i>Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo e(request()->routeIs('easylinkyuswa.info') ? 'active' : ''); ?>"
                           href="<?php echo e(route('easylinkyuswa.info')); ?>">
                            <i class="fas fa-info-circle me-1"></i>Module Info
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo e(request()->routeIs('easylinkyuswa.settings') ? 'active' : ''); ?>"
                           href="<?php echo e(route('easylinkyuswa.settings')); ?>">
                            <i class="fas fa-cog me-1"></i>Settings
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-exchange-alt me-1"></i>Switch Module
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="/easylinkangga">
                                <i class="fas fa-cube me-2"></i>EasyLink Kangangga
                            </a></li>
                            <li><a class="dropdown-item active" href="<?php echo e(route('easylinkyuswa.dashboard')); ?>">
                                <i class="fas fa-fingerprint me-2"></i>EasyLink Yuswa
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="/api/easylinkyuswa/health">
                                <i class="fas fa-heartbeat me-2"></i>API Health Check
                            </a></li>
                        </ul>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item me-3">
                        <span class="navbar-text">
                            <i class="fas fa-circle status-indicator" id="connection-status"></i>
                            <span id="connection-text">Checking...</span>
                        </span>
                    </li>
                    <li class="nav-item">
                        <button class="btn btn-outline-light btn-sm" onclick="refreshAll()">
                            <i class="fas fa-sync-alt me-1" id="refresh-icon"></i>Refresh
                        </button>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container mt-4">
        <?php echo $__env->yieldContent('content'); ?>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-light text-center py-3 mt-5">
        <div class="container">
            <p class="mb-0">&copy; <?php echo e(date('Y')); ?> EasyLink Yuswa Module. Powered by Laravel Attendance System.</p>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    
    <!-- Global JavaScript -->
    <script>
        // CSRF Token Setup
        $.ajaxSetup({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        });

        // Global Variables
        let connectionCheckInterval;
        let isRefreshing = false;

        // Connection Status Check with Enhanced UI
        function checkConnectionStatus() {
            $.get('<?php echo e(route("easylinkyuswa.api.realtime.status")); ?>')
                .done(function(response) {
                    if (response.success && response.data.connected) {
                        $('#connection-status').removeClass('status-offline').addClass('status-online');
                        $('#connection-text').text('Online (' + response.data.response_time + ')');
                        updateConnectionIndicators(true, response.data);
                    } else {
                        $('#connection-status').removeClass('status-online').addClass('status-offline');
                        $('#connection-text').text('Offline');
                        updateConnectionIndicators(false, response.data);
                    }
                })
                .fail(function() {
                    $('#connection-status').removeClass('status-online').addClass('status-offline');
                    $('#connection-text').text('Error');
                    updateConnectionIndicators(false, null);
                });
        }

        // Update connection indicators throughout the page
        function updateConnectionIndicators(connected, data) {
            $('.connection-indicator').each(function() {
                if (connected) {
                    $(this).removeClass('text-danger').addClass('text-success');
                    $(this).find('i').removeClass('fa-times-circle').addClass('fa-check-circle');
                } else {
                    $(this).removeClass('text-success').addClass('text-danger');
                    $(this).find('i').removeClass('fa-check-circle').addClass('fa-times-circle');
                }
            });
        }

        // Enhanced Alert System with SweetAlert2
        function showAlert(message, type = 'info', title = null) {
            const icons = {
                'success': 'success',
                'error': 'error',
                'warning': 'warning',
                'info': 'info',
                'danger': 'error'
            };

            Swal.fire({
                title: title || (type.charAt(0).toUpperCase() + type.slice(1)),
                text: message,
                icon: icons[type] || 'info',
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 3000,
                timerProgressBar: true,
                background: 'rgba(255, 255, 255, 0.95)',
                backdrop: false
            });
        }

        // Enhanced Loading States
        function showLoading(element, text = 'Loading...') {
            const loadingHtml = `
                <div class="d-flex align-items-center justify-content-center p-4">
                    <div class="spinner-border text-primary me-3" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <span class="text-muted">${text}</span>
                </div>
            `;
            $(element).html(loadingHtml);
        }

        // Global Refresh Function
        function refreshAll() {
            if (isRefreshing) return;

            isRefreshing = true;
            const refreshIcon = $('#refresh-icon');
            refreshIcon.addClass('icon-spin');

            // Trigger refresh events
            $(document).trigger('refresh-all');

            setTimeout(() => {
                isRefreshing = false;
                refreshIcon.removeClass('icon-spin');
                showAlert('All data refreshed successfully!', 'success');
            }, 2000);
        }

        // Auto-refresh connection status
        $(document).ready(function() {
            // Initial load with animation
            $('body').addClass('animate__animated animate__fadeIn');

            checkConnectionStatus();
            connectionCheckInterval = setInterval(checkConnectionStatus, 30000);

            // Initialize tooltips
            var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });

            // Initialize popovers
            var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
            var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
                return new bootstrap.Popover(popoverTriggerEl);
            });
        });

        // Utility Functions
        function formatDateTime(dateString) {
            if (!dateString) return 'N/A';
            const date = new Date(dateString);
            return date.toLocaleString('en-US', {
                year: 'numeric',
                month: 'short',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
        }

        function formatResponseTime(responseTime) {
            if (!responseTime) return 'N/A';
            return responseTime.toString().includes('ms') ? responseTime : responseTime + 'ms';
        }

        function formatNumber(num) {
            return new Intl.NumberFormat().format(num);
        }

        function getStatusBadge(status, connected = null) {
            if (connected !== null) {
                return connected ?
                    '<span class="badge bg-success"><i class="fas fa-check me-1"></i>Online</span>' :
                    '<span class="badge bg-danger"><i class="fas fa-times me-1"></i>Offline</span>';
            }

            const badges = {
                'online': '<span class="badge bg-success"><i class="fas fa-check me-1"></i>Online</span>',
                'offline': '<span class="badge bg-danger"><i class="fas fa-times me-1"></i>Offline</span>',
                'success': '<span class="badge bg-success"><i class="fas fa-check me-1"></i>Success</span>',
                'failed': '<span class="badge bg-danger"><i class="fas fa-times me-1"></i>Failed</span>',
                'warning': '<span class="badge bg-warning"><i class="fas fa-exclamation me-1"></i>Warning</span>',
                'info': '<span class="badge bg-info"><i class="fas fa-info me-1"></i>Info</span>'
            };

            return badges[status.toLowerCase()] || `<span class="badge bg-secondary">${status}</span>`;
        }

        // Enhanced Error Handling
        function handleApiError(xhr, textStatus, errorThrown) {
            let message = 'An unexpected error occurred';

            if (xhr.responseJSON && xhr.responseJSON.message) {
                message = xhr.responseJSON.message;
            } else if (xhr.status === 0) {
                message = 'Network connection error';
            } else if (xhr.status === 404) {
                message = 'Requested resource not found';
            } else if (xhr.status === 500) {
                message = 'Internal server error';
            }

            showAlert(message, 'error', 'Error');
        }

        // Setup global AJAX error handler
        $(document).ajaxError(function(event, xhr, settings, thrownError) {
            if (xhr.status !== 0) { // Ignore aborted requests
                handleApiError(xhr, 'error', thrownError);
            }
        });

        // Keyboard shortcuts
        $(document).keydown(function(e) {
            // Ctrl+R or F5 for refresh
            if ((e.ctrlKey && e.keyCode === 82) || e.keyCode === 116) {
                e.preventDefault();
                refreshAll();
            }
        });
    </script>
    
    <?php echo $__env->yieldContent('scripts'); ?>
</body>
</html>
<?php /**PATH C:\laragon\www\larattendance\modules\EasyLinkYuswa\Providers/../Resources/views/layouts/app.blade.php ENDPATH**/ ?>