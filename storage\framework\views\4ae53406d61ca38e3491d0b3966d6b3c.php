<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    
    <title>EasyLink Kangangga - Settings</title>
    
    <?php echo app('Illuminate\Foundation\Vite')(['resources/css/app.css', 'resources/js/app.js']); ?>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1><i class="fas fa-cog"></i> EasyLink Kangangga Settings</h1>
            <p>Module Configuration & Environment Settings</p>
        </div>

        <!-- Navigation -->
        <div class="card" style="margin-bottom: 2rem;">
            <div style="display: flex; gap: 1rem; flex-wrap: wrap;">
                <a href="<?php echo e(route('easylinkangga.dashboard')); ?>" class="btn btn-primary">
                    <i class="fas fa-tachometer-alt"></i> Dashboard
                </a>
                <a href="<?php echo e(route('easylinkangga.info')); ?>" class="btn btn-primary">
                    <i class="fas fa-info-circle"></i> Module Info
                </a>
                <a href="<?php echo e(route('easylinkangga.settings')); ?>" class="btn btn-success">
                    <i class="fas fa-cog"></i> Settings
                </a>
            </div>
        </div>

        <!-- Settings Grid -->
        <div class="dashboard-grid">
            <!-- SDK Configuration -->
            <div class="card">
                <div class="card-header">
                    <div class="card-icon primary">
                        <i class="fas fa-server"></i>
                    </div>
                    <div>
                        <h3 class="card-title">SDK Configuration</h3>
                    </div>
                </div>
                
                <div class="config-item">
                    <span class="config-label">Host</span>
                    <span class="config-value"><?php echo e($settings['sdk_host'] ?? 'Not set'); ?></span>
                </div>
                
                <div class="config-item">
                    <span class="config-label">Serial Number</span>
                    <span class="config-value"><?php echo e($settings['sdk_sn'] ?? 'Not set'); ?></span>
                </div>
                
                <div class="config-item">
                    <span class="config-label">Server Port</span>
                    <span class="config-value"><?php echo e($settings['server_port'] ?? 'Not set'); ?></span>
                </div>
                
                <div class="config-item">
                    <span class="config-label">Timeout</span>
                    <span class="config-value"><?php echo e($settings['timeout'] ?? 30); ?> seconds</span>
                </div>
            </div>

            <!-- Database Configuration -->
            <div class="card">
                <div class="card-header">
                    <div class="card-icon success">
                        <i class="fas fa-database"></i>
                    </div>
                    <div>
                        <h3 class="card-title">Database Configuration</h3>
                    </div>
                </div>
                
                <div class="config-item">
                    <span class="config-label">Host</span>
                    <span class="config-value"><?php echo e($settings['db_host'] ?? 'localhost'); ?></span>
                </div>
                
                <div class="config-item">
                    <span class="config-label">Port</span>
                    <span class="config-value"><?php echo e($settings['db_port'] ?? 3306); ?></span>
                </div>
                
                <div class="config-item">
                    <span class="config-label">Database</span>
                    <span class="config-value"><?php echo e($settings['db_database'] ?? 'Not set'); ?></span>
                </div>
                
                <div class="config-item">
                    <span class="config-label">Username</span>
                    <span class="config-value"><?php echo e($settings['db_username'] ?? 'Not set'); ?></span>
                </div>
                
                <div class="config-item">
                    <span class="config-label">Password</span>
                    <span class="config-value"><?php echo e($settings['db_password'] ? '••••••••' : 'Not set'); ?></span>
                </div>
            </div>

            <!-- Module Settings -->
            <div class="card">
                <div class="card-header">
                    <div class="card-icon warning">
                        <i class="fas fa-toggle-on"></i>
                    </div>
                    <div>
                        <h3 class="card-title">Module Settings</h3>
                    </div>
                </div>
                
                <div class="config-item">
                    <span class="config-label">Module Enabled</span>
                    <span class="config-value">
                        <?php if($settings['enabled'] ?? true): ?>
                            <span style="color: var(--success-color);">✓ Yes</span>
                        <?php else: ?>
                            <span style="color: var(--error-color);">✗ No</span>
                        <?php endif; ?>
                    </span>
                </div>
                
                <div class="config-item">
                    <span class="config-label">Debug Mode</span>
                    <span class="config-value">
                        <?php if($settings['debug'] ?? false): ?>
                            <span style="color: var(--warning-color);">⚠ Enabled</span>
                        <?php else: ?>
                            <span style="color: var(--success-color);">✓ Disabled</span>
                        <?php endif; ?>
                    </span>
                </div>
                
                <div class="config-item">
                    <span class="config-label">Retry Attempts</span>
                    <span class="config-value"><?php echo e($settings['retry_attempts'] ?? 3); ?></span>
                </div>
            </div>

            <!-- Dashboard Settings -->
            <div class="card">
                <div class="card-header">
                    <div class="card-icon error">
                        <i class="fas fa-desktop"></i>
                    </div>
                    <div>
                        <h3 class="card-title">Dashboard Settings</h3>
                    </div>
                </div>
                
                <div class="config-item">
                    <span class="config-label">Title</span>
                    <span class="config-value"><?php echo e($settings['dashboard']['title'] ?? 'EasyLink SDK Dashboard'); ?></span>
                </div>
                
                <div class="config-item">
                    <span class="config-label">Auto Refresh</span>
                    <span class="config-value">
                        <?php if($settings['dashboard']['auto_refresh'] ?? true): ?>
                            <span style="color: var(--success-color);">✓ Enabled</span>
                        <?php else: ?>
                            <span style="color: var(--error-color);">✗ Disabled</span>
                        <?php endif; ?>
                    </span>
                </div>
                
                <div class="config-item">
                    <span class="config-label">Refresh Interval</span>
                    <span class="config-value"><?php echo e($settings['dashboard']['refresh_interval'] ?? 30); ?> seconds</span>
                </div>
                
                <div class="config-item">
                    <span class="config-label">Theme</span>
                    <span class="config-value"><?php echo e($settings['dashboard']['theme'] ?? 'default'); ?></span>
                </div>
            </div>

            <!-- API Settings -->
            <div class="card">
                <div class="card-header">
                    <div class="card-icon primary">
                        <i class="fas fa-code"></i>
                    </div>
                    <div>
                        <h3 class="card-title">API Settings</h3>
                    </div>
                </div>
                
                <div class="config-item">
                    <span class="config-label">Rate Limit</span>
                    <span class="config-value"><?php echo e($settings['api']['rate_limit'] ?? 60); ?> requests/minute</span>
                </div>
                
                <div class="config-item">
                    <span class="config-label">Cache TTL</span>
                    <span class="config-value"><?php echo e($settings['api']['cache_ttl'] ?? 300); ?> seconds</span>
                </div>
                
                <div class="config-item">
                    <span class="config-label">Response Format</span>
                    <span class="config-value"><?php echo e($settings['api']['response_format'] ?? 'json'); ?></span>
                </div>
            </div>

            <!-- Environment Variables -->
            <div class="card">
                <div class="card-header">
                    <div class="card-icon success">
                        <i class="fas fa-file-code"></i>
                    </div>
                    <div>
                        <h3 class="card-title">Environment Variables</h3>
                    </div>
                </div>
                
                <div style="background: var(--gray-50); padding: 1rem; border-radius: 8px; margin-bottom: 1rem;">
                    <h4 style="color: var(--gray-700); margin-bottom: 0.5rem;">Required Variables</h4>
                    <code style="display: block; margin-bottom: 0.25rem; font-size: 0.875rem;">EASYLINK_SDK_HOST=<?php echo e($settings['sdk_host'] ?? 'Not set'); ?></code>
                    <code style="display: block; margin-bottom: 0.25rem; font-size: 0.875rem;">EASYLINK_SDK_SN=<?php echo e($settings['sdk_sn'] ?? 'Not set'); ?></code>
                    <code style="display: block; margin-bottom: 0.25rem; font-size: 0.875rem;">EASYLINK_SERVER_PORT=<?php echo e($settings['server_port'] ?? 'Not set'); ?></code>
                </div>
                
                <div style="background: var(--gray-50); padding: 1rem; border-radius: 8px; margin-bottom: 1rem;">
                    <h4 style="color: var(--gray-700); margin-bottom: 0.5rem;">Database Variables</h4>
                    <code style="display: block; margin-bottom: 0.25rem; font-size: 0.875rem;">EASYLINK_DB_HOST=<?php echo e($settings['db_host'] ?? 'Not set'); ?></code>
                    <code style="display: block; margin-bottom: 0.25rem; font-size: 0.875rem;">EASYLINK_DB_DATABASE=<?php echo e($settings['db_database'] ?? 'Not set'); ?></code>
                    <code style="display: block; margin-bottom: 0.25rem; font-size: 0.875rem;">EASYLINK_DB_USERNAME=<?php echo e($settings['db_username'] ?? 'Not set'); ?></code>
                    <code style="display: block; font-size: 0.875rem;">EASYLINK_DB_PASSWORD=<?php echo e($settings['db_password'] ? '••••••••' : 'Not set'); ?></code>
                </div>
                
                <div style="background: var(--gray-50); padding: 1rem; border-radius: 8px;">
                    <h4 style="color: var(--gray-700); margin-bottom: 0.5rem;">Optional Variables</h4>
                    <code style="display: block; margin-bottom: 0.25rem; font-size: 0.875rem;">EASYLINK_MODULE_ENABLED=<?php echo e($settings['enabled'] ? 'true' : 'false'); ?></code>
                    <code style="display: block; margin-bottom: 0.25rem; font-size: 0.875rem;">EASYLINK_DEBUG=<?php echo e($settings['debug'] ? 'true' : 'false'); ?></code>
                    <code style="display: block; margin-bottom: 0.25rem; font-size: 0.875rem;">EASYLINK_TIMEOUT=<?php echo e($settings['timeout'] ?? 30); ?></code>
                    <code style="display: block; margin-bottom: 0.25rem; font-size: 0.875rem;">EASYLINK_AUTO_REFRESH=<?php echo e($settings['dashboard']['auto_refresh'] ? 'true' : 'false'); ?></code>
                    <code style="display: block; font-size: 0.875rem;">EASYLINK_REFRESH_INTERVAL=<?php echo e($settings['dashboard']['refresh_interval'] ?? 30); ?></code>
                </div>
            </div>
        </div>

        <!-- Actions -->
        <div class="card">
            <div class="card-header">
                <div class="card-icon primary">
                    <i class="fas fa-tools"></i>
                </div>
                <div>
                    <h3 class="card-title">Configuration Actions</h3>
                </div>
            </div>
            
            <div style="display: flex; gap: 1rem; flex-wrap: wrap; margin-bottom: 1rem;">
                <button onclick="testConfiguration()" class="btn btn-primary">
                    <i class="fas fa-vial"></i> Test Configuration
                </button>
                <button onclick="clearCache()" class="btn btn-warning">
                    <i class="fas fa-trash"></i> Clear Cache
                </button>
                <button onclick="exportConfig()" class="btn btn-success">
                    <i class="fas fa-download"></i> Export Config
                </button>
            </div>
            
            <div id="action-result"></div>
        </div>

        <!-- Footer -->
        <div style="text-align: center; margin-top: 3rem; color: white; opacity: 0.8;">
            <p>EasyLink Kangangga Module Settings | Last Updated: <?php echo e(now()->format('Y-m-d H:i:s')); ?></p>
        </div>
    </div>

    <script>
        // Configuration data from server
        window.easylinkConfig = <?php echo json_encode($settings, 15, 512) ?>;

        async function testConfiguration() {
            const resultDiv = document.getElementById('action-result');
            resultDiv.innerHTML = '<div class="alert alert-warning"><i class="fas fa-spinner fa-spin"></i> Testing configuration...</div>';

            try {
                const response = await fetch('/easylinkangga/api/configuration');
                const data = await response.json();

                if (data.success) {
                    resultDiv.innerHTML = '<div class="alert alert-success"><i class="fas fa-check-circle"></i> Configuration test successful!</div>';
                } else {
                    resultDiv.innerHTML = '<div class="alert alert-error"><i class="fas fa-exclamation-circle"></i> Configuration test failed!</div>';
                }
            } catch (error) {
                resultDiv.innerHTML = '<div class="alert alert-error"><i class="fas fa-exclamation-circle"></i> Error: ' + error.message + '</div>';
            }
        }

        async function clearCache() {
            const resultDiv = document.getElementById('action-result');
            resultDiv.innerHTML = '<div class="alert alert-warning"><i class="fas fa-spinner fa-spin"></i> Clearing cache...</div>';

            try {
                const response = await fetch('/easylinkangga/api/clear-cache', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                });

                const data = await response.json();

                if (data.success) {
                    let message = '<div class="alert alert-success"><i class="fas fa-check-circle"></i> ' + data.message + '<br>';
                    for (const [key, value] of Object.entries(data.cleared)) {
                        message += '<small>• ' + value + '</small><br>';
                    }
                    message += '</div>';
                    resultDiv.innerHTML = message;
                } else {
                    resultDiv.innerHTML = '<div class="alert alert-error"><i class="fas fa-exclamation-circle"></i> ' + data.message + '</div>';
                }
            } catch (error) {
                resultDiv.innerHTML = '<div class="alert alert-error"><i class="fas fa-exclamation-circle"></i> Error: ' + error.message + '</div>';
            }
        }

        function exportConfig() {
            const config = window.easylinkConfig;
            const dataStr = "data:text/json;charset=utf-8," + encodeURIComponent(JSON.stringify(config, null, 2));
            const downloadAnchorNode = document.createElement('a');
            downloadAnchorNode.setAttribute("href", dataStr);
            downloadAnchorNode.setAttribute("download", "easylink-kangangga-config.json");
            document.body.appendChild(downloadAnchorNode);
            downloadAnchorNode.click();
            downloadAnchorNode.remove();

            const resultDiv = document.getElementById('action-result');
            resultDiv.innerHTML = '<div class="alert alert-success"><i class="fas fa-download"></i> Configuration exported successfully!</div>';
        }
    </script>
</body>
</html>
<?php /**PATH C:\laragon\www\larattendance\modules\EasylinkKangangga\Resources/views/settings.blade.php ENDPATH**/ ?>