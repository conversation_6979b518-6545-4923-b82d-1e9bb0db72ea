<?php $__env->startSection('title', 'EasyLink Yuswa Dashboard'); ?>

<?php $__env->startSection('content'); ?>
<div class="row">
    <!-- Page Header -->
    <div class="col-12 mb-4">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="h3 text-gradient">
                    <i class="fas fa-tachometer-alt me-2"></i>EasyLink Yuswa Dashboard
                </h1>
                <p class="text-muted">Monitor and manage your Fingerspot EasyLink device integration</p>
            </div>
            <div>
                <button class="btn btn-primary me-2" onclick="refreshDashboard()">
                    <i class="fas fa-sync-alt me-1"></i>Refresh
                </button>
                <button class="btn btn-outline-primary" onclick="testModule()">
                    <i class="fas fa-vial me-1"></i>Test Module
                </button>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Connection Status Card -->
    <div class="col-md-6 col-lg-3 mb-4">
        <div class="card h-100">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-wifi me-2"></i>Connection Status
                </h6>
            </div>
            <div class="card-body text-center">
                <div id="connection-status-card">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2 mb-0">Checking connection...</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Device Info Card -->
    <div class="col-md-6 col-lg-3 mb-4">
        <div class="card h-100">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-microchip me-2"></i>Device Info
                </h6>
            </div>
            <div class="card-body">
                <div id="device-info-card">
                    <div class="spinner-border spinner-border-sm text-primary" role="status"></div>
                    <span class="ms-2">Loading...</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Card -->
    <div class="col-md-6 col-lg-3 mb-4">
        <div class="card h-100">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-chart-bar me-2"></i>Statistics
                </h6>
            </div>
            <div class="card-body">
                <div id="stats-card">
                    <div class="spinner-border spinner-border-sm text-primary" role="status"></div>
                    <span class="ms-2">Loading...</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions Card -->
    <div class="col-md-6 col-lg-3 mb-4">
        <div class="card h-100">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-bolt me-2"></i>Quick Actions
                </h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <button class="btn btn-sm btn-outline-primary" onclick="testDevice()">
                        <i class="fas fa-stethoscope me-1"></i>Test Device
                    </button>
                    <button class="btn btn-sm btn-outline-success" onclick="syncAttendance()">
                        <i class="fas fa-sync me-1"></i>Sync Data
                    </button>
                    <button class="btn btn-sm btn-outline-warning" onclick="clearCache()">
                        <i class="fas fa-broom me-1"></i>Clear Cache
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Configuration Card -->
    <div class="col-lg-6 mb-4">
        <div class="card h-100">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-cogs me-2"></i>Configuration
                </h6>
            </div>
            <div class="card-body">
                <div id="config-card">
                    <div class="spinner-border spinner-border-sm text-primary" role="status"></div>
                    <span class="ms-2">Loading configuration...</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Attendance Logs -->
    <div class="col-lg-6 mb-4">
        <div class="card h-100">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h6 class="card-title mb-0">
                    <i class="fas fa-clock me-2"></i>Recent Attendance
                </h6>
                <button class="btn btn-sm btn-outline-light" onclick="loadAttendanceLogs()">
                    <i class="fas fa-refresh me-1"></i>Refresh
                </button>
            </div>
            <div class="card-body">
                <div id="attendance-logs-card">
                    <div class="spinner-border spinner-border-sm text-primary" role="status"></div>
                    <span class="ms-2">Loading attendance logs...</span>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Test Results Modal -->
<div class="modal fade" id="testResultsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-vial me-2"></i>Module Test Results
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="test-results-content">
                    <!-- Test results will be loaded here -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" onclick="testModule()">Run Test Again</button>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<script>
$(document).ready(function() {
    loadDashboardData();
    
    // Auto-refresh dashboard data every 60 seconds
    setInterval(loadDashboardData, 60000);
});

function loadDashboardData() {
    $.get('<?php echo e(route("easylinkyuswa.api.dashboard.data")); ?>')
        .done(function(response) {
            if (response.success) {
                updateConnectionStatus(response.data.connectivity);
                updateDeviceInfo(response.data.connectivity);
                updateStats(response.data.stats);
                updateConfiguration(response.data.config);
            }
        })
        .fail(function() {
            showAlert('Failed to load dashboard data', 'danger');
        });
}

function updateConnectionStatus(connectivity) {
    const statusHtml = connectivity.connected ? 
        `<i class="fas fa-check-circle text-success fa-3x mb-2"></i>
         <h5 class="text-success">Online</h5>
         <p class="mb-0">Response: ${connectivity.response_time}</p>
         <small class="text-muted">Last check: ${formatDateTime(connectivity.last_check)}</small>` :
        `<i class="fas fa-times-circle text-danger fa-3x mb-2"></i>
         <h5 class="text-danger">Offline</h5>
         <p class="mb-0">Connection failed</p>
         <small class="text-muted">Last check: ${formatDateTime(connectivity.last_check)}</small>`;
    
    $('#connection-status-card').html(statusHtml);
}

function updateDeviceInfo(connectivity) {
    const deviceHtml = connectivity.connected ?
        `<p class="mb-1"><strong>Host:</strong> ${connectivity.host}</p>
         <p class="mb-1"><strong>Serial:</strong> ${connectivity.serial_number}</p>
         <p class="mb-0"><strong>Status:</strong> <span class="text-success">${connectivity.status}</span></p>` :
        `<p class="mb-1"><strong>Host:</strong> ${connectivity.host}</p>
         <p class="mb-1"><strong>Serial:</strong> ${connectivity.serial_number}</p>
         <p class="mb-0"><strong>Status:</strong> <span class="text-danger">${connectivity.status}</span></p>`;
    
    $('#device-info-card').html(deviceHtml);
}

function updateStats(stats) {
    const statsHtml = `
        <p class="mb-1"><strong>Module:</strong> <span class="badge bg-${stats.module_enabled ? 'success' : 'danger'}">${stats.module_enabled ? 'Enabled' : 'Disabled'}</span></p>
        <p class="mb-1"><strong>Device:</strong> <span class="badge bg-${stats.device_connected ? 'success' : 'danger'}">${stats.device_connected ? 'Connected' : 'Disconnected'}</span></p>
        <p class="mb-1"><strong>Total Logs:</strong> ${stats.total_logs}</p>
        <p class="mb-0"><strong>Errors:</strong> ${stats.errors_count}</p>
    `;
    $('#stats-card').html(statsHtml);
}

function updateConfiguration(config) {
    const configHtml = `
        <div class="row">
            <div class="col-6">
                <p class="mb-1"><strong>Module:</strong> ${config.module_name}</p>
                <p class="mb-1"><strong>Alias:</strong> ${config.module_alias}</p>
                <p class="mb-1"><strong>Host:</strong> ${config.sdk_host}</p>
            </div>
            <div class="col-6">
                <p class="mb-1"><strong>Port:</strong> ${config.server_port}</p>
                <p class="mb-1"><strong>Timeout:</strong> ${config.timeout}s</p>
                <p class="mb-1"><strong>Debug:</strong> ${config.debug ? 'On' : 'Off'}</p>
            </div>
        </div>
    `;
    $('#config-card').html(configHtml);
}

function loadAttendanceLogs() {
    $('#attendance-logs-card').html('<div class="spinner-border spinner-border-sm text-primary" role="status"></div><span class="ms-2">Loading...</span>');
    
    $.get('<?php echo e(route("easylinkyuswa.api.attendance.logs")); ?>')
        .done(function(response) {
            if (response.success && response.data.length > 0) {
                let logsHtml = '<div class="table-responsive"><table class="table table-sm"><thead><tr><th>Employee</th><th>Date</th><th>Time</th></tr></thead><tbody>';
                response.data.slice(0, 5).forEach(function(log) {
                    const date = new Date(log.scan_date);
                    logsHtml += `<tr><td>${log.employee_id || 'N/A'}</td><td>${date.toLocaleDateString()}</td><td>${date.toLocaleTimeString()}</td></tr>`;
                });
                logsHtml += '</tbody></table></div>';
                logsHtml += `<small class="text-muted">Showing latest 5 of ${response.count} records</small>`;
                $('#attendance-logs-card').html(logsHtml);
            } else {
                $('#attendance-logs-card').html('<p class="text-muted mb-0">No attendance logs available</p>');
            }
        })
        .fail(function() {
            $('#attendance-logs-card').html('<p class="text-danger mb-0">Failed to load attendance logs</p>');
        });
}

function refreshDashboard() {
    showAlert('Refreshing dashboard...', 'info');
    loadDashboardData();
    loadAttendanceLogs();
}

function testModule() {
    $('#testResultsModal').modal('show');
    $('#test-results-content').html('<div class="text-center"><div class="spinner-border" role="status"></div><p class="mt-2">Running module tests...</p></div>');
    
    $.get('<?php echo e(route("easylinkyuswa.api.test.module")); ?>')
        .done(function(response) {
            let resultsHtml = `<div class="alert alert-${response.overall_test_result === 'passed' ? 'success' : 'danger'}">
                <h6><i class="fas fa-${response.overall_test_result === 'passed' ? 'check' : 'times'} me-2"></i>Overall Test Result: ${response.overall_test_result.toUpperCase()}</h6>
            </div>`;
            
            resultsHtml += '<div class="row">';
            resultsHtml += `<div class="col-md-4"><h6>Connectivity</h6><span class="badge bg-${response.data.connectivity.connected ? 'success' : 'danger'}">${response.data.connectivity.connected ? 'PASS' : 'FAIL'}</span></div>`;
            resultsHtml += `<div class="col-md-4"><h6>Device Info</h6><span class="badge bg-${response.data.device_info_status === 'success' ? 'success' : 'danger'}">${response.data.device_info_status === 'success' ? 'PASS' : 'FAIL'}</span></div>`;
            resultsHtml += `<div class="col-md-4"><h6>Attendance Logs</h6><span class="badge bg-${response.data.attendance_logs_status === 'success' ? 'success' : 'danger'}">${response.data.attendance_logs_status === 'success' ? 'PASS' : 'FAIL'}</span></div>`;
            resultsHtml += '</div>';
            
            resultsHtml += `<hr><small class="text-muted">Test completed at: ${formatDateTime(response.test_timestamp)}</small>`;
            
            $('#test-results-content').html(resultsHtml);
        })
        .fail(function() {
            $('#test-results-content').html('<div class="alert alert-danger">Test failed to execute</div>');
        });
}

function testDevice() {
    $.get('<?php echo e(route("easylinkyuswa.api.test.device")); ?>')
        .done(function(response) {
            if (response.success) {
                showAlert('Device test completed successfully', 'success');
                loadDashboardData();
            } else {
                showAlert('Device test failed: ' + response.message, 'danger');
            }
        })
        .fail(function() {
            showAlert('Device test failed to execute', 'danger');
        });
}

function syncAttendance() {
    $.post('<?php echo e(route("easylinkyuswa.api.sync.attendance")); ?>')
        .done(function(response) {
            if (response.success) {
                showAlert(`Attendance synchronized: ${response.data.count} records`, 'success');
                loadAttendanceLogs();
            } else {
                showAlert('Sync failed: ' + response.message, 'danger');
            }
        })
        .fail(function() {
            showAlert('Sync failed to execute', 'danger');
        });
}

function clearCache() {
    $.post('<?php echo e(route("easylinkyuswa.api.cache.clear")); ?>')
        .done(function(response) {
            if (response.success) {
                showAlert('Cache cleared successfully', 'success');
            } else {
                showAlert('Failed to clear cache: ' + response.message, 'danger');
            }
        })
        .fail(function() {
            showAlert('Failed to clear cache', 'danger');
        });
}

// Load attendance logs on page load
$(document).ready(function() {
    loadAttendanceLogs();
});
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('easylinkyuswa::layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\laragon\www\larattendance\modules\EasyLinkYuswa\Providers/../Resources/views/dashboard.blade.php ENDPATH**/ ?>