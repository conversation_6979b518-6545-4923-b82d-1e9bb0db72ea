<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    
    <title><?php echo e($moduleInfo['name']); ?> - Module Information</title>
    
    <?php echo app('Illuminate\Foundation\Vite')(['resources/css/app.css', 'resources/js/app.js']); ?>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1><i class="fas fa-info-circle"></i> <?php echo e($moduleInfo['name']); ?></h1>
            <p>Module Information & Details</p>
        </div>

        <!-- Navigation -->
        <div class="card" style="margin-bottom: 2rem;">
            <div style="display: flex; gap: 1rem; flex-wrap: wrap;">
                <a href="<?php echo e(route('easylinkangga.dashboard')); ?>" class="btn btn-primary">
                    <i class="fas fa-tachometer-alt"></i> Dashboard
                </a>
                <a href="<?php echo e(route('easylinkangga.info')); ?>" class="btn btn-success">
                    <i class="fas fa-info-circle"></i> Module Info
                </a>
                <a href="<?php echo e(route('easylinkangga.settings')); ?>" class="btn btn-primary">
                    <i class="fas fa-cog"></i> Settings
                </a>
            </div>
        </div>

        <!-- Module Information -->
        <div class="dashboard-grid">
            <!-- Basic Information -->
            <div class="card">
                <div class="card-header">
                    <div class="card-icon primary">
                        <i class="fas fa-cube"></i>
                    </div>
                    <div>
                        <h3 class="card-title">Module Details</h3>
                    </div>
                </div>
                
                <div class="config-item">
                    <span class="config-label">Name</span>
                    <span class="config-value"><?php echo e($moduleInfo['name']); ?></span>
                </div>
                
                <div class="config-item">
                    <span class="config-label">Alias</span>
                    <span class="config-value"><?php echo e($moduleInfo['alias']); ?></span>
                </div>
                
                <div class="config-item">
                    <span class="config-label">Version</span>
                    <span class="config-value"><?php echo e($moduleInfo['version']); ?></span>
                </div>
                
                <div class="config-item">
                    <span class="config-label">Author</span>
                    <span class="config-value"><?php echo e($moduleInfo['author']); ?></span>
                </div>
                
                <div class="config-item">
                    <span class="config-label">Package</span>
                    <span class="config-value"><?php echo e($moduleInfo['package']); ?></span>
                </div>
            </div>

            <!-- Description -->
            <div class="card">
                <div class="card-header">
                    <div class="card-icon success">
                        <i class="fas fa-file-alt"></i>
                    </div>
                    <div>
                        <h3 class="card-title">Description</h3>
                    </div>
                </div>
                
                <p style="color: var(--gray-600); line-height: 1.6;">
                    <?php echo e($moduleInfo['description']); ?>

                </p>
                
                <div style="margin-top: 1.5rem;">
                    <h4 style="color: var(--gray-700); margin-bottom: 1rem;">Features</h4>
                    <ul style="color: var(--gray-600); line-height: 1.8;">
                        <li>Real-time device connectivity testing</li>
                        <li>Attendance log retrieval and formatting</li>
                        <li>Device information and status monitoring</li>
                        <li>Modern web dashboard interface</li>
                        <li>RESTful API endpoints</li>
                        <li>Modular Laravel architecture</li>
                    </ul>
                </div>
            </div>

            <!-- Technical Information -->
            <div class="card">
                <div class="card-header">
                    <div class="card-icon warning">
                        <i class="fas fa-code"></i>
                    </div>
                    <div>
                        <h3 class="card-title">Technical Details</h3>
                    </div>
                </div>
                
                <div class="config-item">
                    <span class="config-label">Laravel Version</span>
                    <span class="config-value"><?php echo e(Illuminate\Foundation\Application::VERSION); ?></span>
                </div>
                
                <div class="config-item">
                    <span class="config-label">PHP Version</span>
                    <span class="config-value"><?php echo e(PHP_VERSION); ?></span>
                </div>
                
                <div class="config-item">
                    <span class="config-label">Module Path</span>
                    <span class="config-value">modules/EasylinkKangangga</span>
                </div>
                
                <div class="config-item">
                    <span class="config-label">Namespace</span>
                    <span class="config-value">Modules\EasylinkKangangga</span>
                </div>
                
                <div class="config-item">
                    <span class="config-label">Route Prefix</span>
                    <span class="config-value">/easylinkangga</span>
                </div>
            </div>

            <!-- Dependencies -->
            <div class="card">
                <div class="card-header">
                    <div class="card-icon error">
                        <i class="fas fa-puzzle-piece"></i>
                    </div>
                    <div>
                        <h3 class="card-title">Dependencies</h3>
                    </div>
                </div>
                
                <div style="margin-bottom: 1rem;">
                    <h4 style="color: var(--gray-700); margin-bottom: 0.5rem;">Required Packages</h4>
                    <ul style="color: var(--gray-600); line-height: 1.8;">
                        <li><code>kangangga/laravel-easylink</code> - Main EasyLink SDK</li>
                        <li><code>laravel/framework</code> - Laravel Framework</li>
                        <li><code>guzzlehttp/guzzle</code> - HTTP Client</li>
                    </ul>
                </div>
                
                <div>
                    <h4 style="color: var(--gray-700); margin-bottom: 0.5rem;">Frontend Dependencies</h4>
                    <ul style="color: var(--gray-600); line-height: 1.8;">
                        <li><code>Vite</code> - Asset bundling</li>
                        <li><code>Axios</code> - HTTP requests</li>
                        <li><code>Font Awesome</code> - Icons</li>
                        <li><code>Google Fonts</code> - Typography</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- API Documentation -->
        <div class="card">
            <div class="card-header">
                <div class="card-icon primary">
                    <i class="fas fa-book"></i>
                </div>
                <div>
                    <h3 class="card-title">API Documentation</h3>
                </div>
            </div>
            
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(400px, 1fr)); gap: 2rem;">
                <div>
                    <h4 style="color: var(--gray-700); margin-bottom: 1rem;">Web Routes</h4>
                    <div style="background: var(--gray-50); padding: 1rem; border-radius: 8px;">
                        <code style="display: block; margin-bottom: 0.5rem;">GET /easylinkangga</code>
                        <code style="display: block; margin-bottom: 0.5rem;">GET /easylinkangga/dashboard</code>
                        <code style="display: block; margin-bottom: 0.5rem;">GET /easylinkangga/info</code>
                        <code style="display: block;">GET /easylinkangga/settings</code>
                    </div>
                </div>
                
                <div>
                    <h4 style="color: var(--gray-700); margin-bottom: 1rem;">API Routes</h4>
                    <div style="background: var(--gray-50); padding: 1rem; border-radius: 8px;">
                        <code style="display: block; margin-bottom: 0.5rem;">GET /api/easylinkangga/device/info</code>
                        <code style="display: block; margin-bottom: 0.5rem;">GET /api/easylinkangga/attendance/logs</code>
                        <code style="display: block; margin-bottom: 0.5rem;">GET /api/easylinkangga/configuration</code>
                        <code style="display: block;">GET /api/easylinkangga/health</code>
                    </div>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <div style="text-align: center; margin-top: 3rem; color: white; opacity: 0.8;">
            <p><?php echo e($moduleInfo['name']); ?> v<?php echo e($moduleInfo['version']); ?> | Powered by <?php echo e($moduleInfo['package']); ?></p>
        </div>
    </div>
</body>
</html>
<?php /**PATH C:\laragon\www\larattendance\modules\EasylinkKangangga\Resources/views/info.blade.php ENDPATH**/ ?>