<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    
    <title><?php echo e($config['dashboard_title'] ?? 'EasyLink SDK Dashboard'); ?></title>
    
    <?php echo app('Illuminate\Foundation\Vite')(['resources/css/app.css', 'resources/js/app.js']); ?>
</head>
<body>
    <div id="easylink-dashboard" class="container" data-module="easylinkangga">
        <!-- Header -->
        <div class="header">
            <h1><i class="fas fa-fingerprint"></i> <?php echo e($config['module_name'] ?? 'EasyLink SDK'); ?></h1>
            <p><?php echo e($config['dashboard_title'] ?? 'Test Dashboard & Configuration Manager'); ?></p>
            <div style="margin-top: 1rem;">
                <span style="background: rgba(255,255,255,0.2); padding: 0.5rem 1rem; border-radius: 20px; font-size: 0.875rem;">
                    Module: <?php echo e($config['module_alias'] ?? 'easylinkangga'); ?> | Host: <?php echo e($config['sdk_host'] ?? 'Not configured'); ?>

                </span>
            </div>
        </div>

        <!-- Status Overview -->
        <div class="card" style="margin-bottom: 2rem;">
            <div class="card-header">
                <div class="card-icon primary">
                    <i class="fas fa-info-circle"></i>
                </div>
                <div>
                    <h3 class="card-title">System Status</h3>
                </div>
            </div>
            
            <div style="display: flex; gap: 1rem; align-items: center; flex-wrap: wrap;">
                <div id="connection-status" class="status-indicator status-disconnected">
                    <i class="fas fa-times-circle"></i> Disconnected
                </div>
                
                <label style="display: flex; align-items: center; gap: 0.5rem; margin-left: auto;">
                    <input type="checkbox" id="auto-refresh" 
                           <?php echo e(($config['auto_refresh'] ?? true) ? 'checked' : ''); ?> 
                           style="margin: 0;">
                    <span>Auto-refresh connectivity (<?php echo e($config['refresh_interval'] ?? 30); ?>s)</span>
                </label>
            </div>
        </div>

        <!-- Dashboard Grid -->
        <div class="dashboard-grid">
            <!-- Configuration Card -->
            <div class="card">
                <div class="card-header">
                    <div class="card-icon primary">
                        <i class="fas fa-cog"></i>
                    </div>
                    <div>
                        <h3 class="card-title">Module Configuration</h3>
                    </div>
                </div>
                
                <div id="config-display">
                    <div style="text-align: center; color: var(--gray-500); padding: 2rem;">
                        <i class="fas fa-spinner fa-spin"></i> Loading configuration...
                    </div>
                </div>
                
                <div style="margin-top: 1.5rem;">
                    <button id="test-config" class="btn btn-primary">
                        <i class="fas fa-sync"></i> Refresh Config
                    </button>
                </div>
                
                <div id="config-result"></div>
            </div>

            <!-- Connectivity Test Card -->
            <div class="card">
                <div class="card-header">
                    <div class="card-icon warning">
                        <i class="fas fa-wifi"></i>
                    </div>
                    <div>
                        <h3 class="card-title">Device Connectivity</h3>
                    </div>
                </div>
                
                <p style="color: var(--gray-600); margin-bottom: 1.5rem;">
                    Test connection to the EasyLink device and verify network accessibility.
                </p>
                
                <button id="test-connectivity" class="btn btn-warning">
                    <i class="fas fa-satellite-dish"></i> Test Connection
                </button>
                
                <div id="connectivity-result"></div>
            </div>

            <!-- Device Information Card -->
            <div class="card">
                <div class="card-header">
                    <div class="card-icon success">
                        <i class="fas fa-microchip"></i>
                    </div>
                    <div>
                        <h3 class="card-title">Device Information</h3>
                    </div>
                </div>
                
                <p style="color: var(--gray-600); margin-bottom: 1.5rem;">
                    Retrieve detailed information about the connected EasyLink device.
                </p>
                
                <button id="test-device" class="btn btn-success">
                    <i class="fas fa-info"></i> Get Device Info
                </button>
                
                <div id="device-result"></div>
            </div>

            <!-- Attendance Logs Card -->
            <div class="card">
                <div class="card-header">
                    <div class="card-icon error">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div>
                        <h3 class="card-title">Attendance Logs</h3>
                    </div>
                </div>
                
                <p style="color: var(--gray-600); margin-bottom: 1.5rem;">
                    Fetch the latest attendance scan logs from the device.
                </p>
                
                <button id="test-attendance" class="btn btn-error">
                    <i class="fas fa-download"></i> Get Attendance Logs
                </button>
                
                <div id="attendance-result"></div>
            </div>
        </div>

        <!-- API Endpoints Reference -->
        <div class="card">
            <div class="card-header">
                <div class="card-icon primary">
                    <i class="fas fa-code"></i>
                </div>
                <div>
                    <h3 class="card-title">Module API Endpoints</h3>
                </div>
            </div>
            
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 1rem;">
                <div>
                    <h4 style="color: var(--gray-700); margin-bottom: 0.5rem;">Configuration</h4>
                    <code style="background: var(--gray-100); padding: 0.5rem; border-radius: 4px; display: block; font-size: 0.875rem;">
                        GET /easylinkangga/api/configuration
                    </code>
                </div>
                
                <div>
                    <h4 style="color: var(--gray-700); margin-bottom: 0.5rem;">Connectivity Check</h4>
                    <code style="background: var(--gray-100); padding: 0.5rem; border-radius: 4px; display: block; font-size: 0.875rem;">
                        GET /easylinkangga/api/check-connectivity
                    </code>
                </div>
                
                <div>
                    <h4 style="color: var(--gray-700); margin-bottom: 0.5rem;">Device Information</h4>
                    <code style="background: var(--gray-100); padding: 0.5rem; border-radius: 4px; display: block; font-size: 0.875rem;">
                        GET /easylinkangga/api/device-info
                    </code>
                </div>
                
                <div>
                    <h4 style="color: var(--gray-700); margin-bottom: 0.5rem;">Attendance Logs</h4>
                    <code style="background: var(--gray-100); padding: 0.5rem; border-radius: 4px; display: block; font-size: 0.875rem;">
                        GET /easylinkangga/api/attendance-logs
                    </code>
                </div>
            </div>
        </div>

        <!-- Module Navigation -->
        <div class="card">
            <div class="card-header">
                <div class="card-icon primary">
                    <i class="fas fa-sitemap"></i>
                </div>
                <div>
                    <h3 class="card-title">Module Navigation</h3>
                </div>
            </div>
            
            <div style="display: flex; gap: 1rem; flex-wrap: wrap;">
                <a href="<?php echo e(route('easylinkangga.dashboard')); ?>" class="btn btn-primary">
                    <i class="fas fa-tachometer-alt"></i> Dashboard
                </a>
                <a href="<?php echo e(route('easylinkangga.info')); ?>" class="btn btn-primary">
                    <i class="fas fa-info-circle"></i> Module Info
                </a>
                <a href="<?php echo e(route('easylinkangga.settings')); ?>" class="btn btn-primary">
                    <i class="fas fa-cog"></i> Settings
                </a>
            </div>
        </div>

        <!-- Footer -->
        <div style="text-align: center; margin-top: 3rem; color: white; opacity: 0.8;">
            <p>EasyLink Kangangga Module v1.0 | Laravel <?php echo e(Illuminate\Foundation\Application::VERSION); ?> | PHP <?php echo e(PHP_VERSION); ?></p>
        </div>
    </div>

    <script>
        // Update the dashboard JavaScript to use module-specific endpoints
        document.addEventListener('DOMContentLoaded', function() {
            if (typeof EasylinkDashboard !== 'undefined') {
                // Override the API endpoints for the module
                const dashboard = new EasylinkDashboard();
                dashboard.apiPrefix = '/easylinkangga/api';
                dashboard.autoRefreshInterval = <?php echo e($config['refresh_interval'] ?? 30); ?> * 1000;
            }
        });
    </script>
</body>
</html>
<?php /**PATH C:\laragon\www\larattendance\modules\EasylinkKangangga\Resources/views/dashboard.blade.php ENDPATH**/ ?>