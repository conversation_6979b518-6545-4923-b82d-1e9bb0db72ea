<?php $__env->startSection('title', 'EasyLink Yuswa Dashboard'); ?>

<?php $__env->startSection('content'); ?>
<!-- Page Header with Enhanced Design -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card border-0 bg-transparent">
            <div class="card-body p-0">
                <div class="d-flex justify-content-between align-items-center flex-wrap">
                    <div class="animate__animated animate__fadeInLeft">
                        <h1 class="display-6 text-gradient mb-2">
                            <i class="fas fa-tachometer-alt me-3"></i>EasyLink Yuswa Dashboard
                        </h1>
                        <p class="lead text-muted mb-0">
                            <i class="fas fa-fingerprint me-2"></i>Monitor and manage your Fingerspot EasyLink device integration
                        </p>
                        <div class="mt-2">
                            <span class="badge bg-primary me-2">
                                <i class="fas fa-code me-1"></i>Yuswa Arba SDK
                            </span>
                            <span class="badge bg-info me-2">
                                <i class="fas fa-server me-1"></i>Real-time Monitoring
                            </span>
                            <span class="badge bg-success">
                                <i class="fas fa-shield-alt me-1"></i>Secure Connection
                            </span>
                        </div>
                    </div>
                    <div class="animate__animated animate__fadeInRight">
                        <div class="btn-group" role="group">
                            <button class="btn btn-primary" onclick="refreshDashboard()" data-bs-toggle="tooltip" title="Refresh all data">
                                <i class="fas fa-sync-alt me-1"></i>Refresh
                            </button>
                            <button class="btn btn-success" onclick="testModule()" data-bs-toggle="tooltip" title="Run comprehensive tests">
                                <i class="fas fa-vial me-1"></i>Test Module
                            </button>
                            <button class="btn btn-outline-primary" onclick="exportData()" data-bs-toggle="tooltip" title="Export configuration">
                                <i class="fas fa-download me-1"></i>Export
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Connection Status Card -->
    <div class="col-md-6 col-xl-3 mb-4">
        <div class="card stats-card h-100 animate__animated animate__fadeInUp" style="animation-delay: 0.1s">
            <div class="card-body text-center">
                <div class="mb-3">
                    <i class="fas fa-wifi fa-3x text-primary"></i>
                </div>
                <h5 class="card-title text-gradient">Connection Status</h5>
                <div id="connection-status-card">
                    <div class="loading-skeleton rounded" style="height: 60px;"></div>
                </div>
                <div class="mt-3">
                    <div class="progress" style="height: 4px;">
                        <div class="progress-bar" id="connection-progress" style="width: 0%"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Device Info Card -->
    <div class="col-md-6 col-xl-3 mb-4">
        <div class="card stats-card h-100 animate__animated animate__fadeInUp" style="animation-delay: 0.2s">
            <div class="card-body text-center">
                <div class="mb-3">
                    <i class="fas fa-microchip fa-3x text-info"></i>
                </div>
                <h5 class="card-title text-gradient">Device Info</h5>
                <div id="device-info-card">
                    <div class="loading-skeleton rounded" style="height: 80px;"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Card -->
    <div class="col-md-6 col-xl-3 mb-4">
        <div class="card stats-card h-100 animate__animated animate__fadeInUp" style="animation-delay: 0.3s">
            <div class="card-body text-center">
                <div class="mb-3">
                    <i class="fas fa-chart-line fa-3x text-success"></i>
                </div>
                <h5 class="card-title text-gradient">Statistics</h5>
                <div id="stats-card">
                    <div class="loading-skeleton rounded" style="height: 100px;"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions Card -->
    <div class="col-md-6 col-xl-3 mb-4">
        <div class="card stats-card h-100 animate__animated animate__fadeInUp" style="animation-delay: 0.4s">
            <div class="card-body text-center">
                <div class="mb-3">
                    <i class="fas fa-bolt fa-3x text-warning"></i>
                </div>
                <h5 class="card-title text-gradient">Quick Actions</h5>
                <div class="d-grid gap-2">
                    <button class="btn btn-primary btn-sm" onclick="testDevice()" data-bs-toggle="tooltip" title="Test device connectivity">
                        <i class="fas fa-stethoscope me-1"></i>Test Device
                    </button>
                    <button class="btn btn-success btn-sm" onclick="syncAttendance()" data-bs-toggle="tooltip" title="Synchronize attendance data">
                        <i class="fas fa-sync me-1"></i>Sync Data
                    </button>
                    <button class="btn btn-warning btn-sm" onclick="clearCache()" data-bs-toggle="tooltip" title="Clear application cache">
                        <i class="fas fa-broom me-1"></i>Clear Cache
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Configuration Card -->
    <div class="col-lg-8 mb-4">
        <div class="card h-100 animate__animated animate__fadeInUp" style="animation-delay: 0.5s">
            <div class="card-header d-flex justify-content-between align-items-center">
                <div>
                    <h5 class="card-title mb-0">
                        <i class="fas fa-cogs me-2"></i>System Configuration
                    </h5>
                    <small class="text-light opacity-75">Current module settings and device configuration</small>
                </div>
                <button class="btn btn-outline-light btn-sm" onclick="refreshConfig()" data-bs-toggle="tooltip" title="Refresh configuration">
                    <i class="fas fa-sync-alt"></i>
                </button>
            </div>
            <div class="card-body">
                <div id="config-card">
                    <div class="loading-skeleton rounded" style="height: 200px;"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- System Status & Metrics -->
    <div class="col-lg-4 mb-4">
        <div class="card h-100 animate__animated animate__fadeInUp" style="animation-delay: 0.6s">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-heartbeat me-2"></i>System Health
                </h5>
                <small class="text-light opacity-75">Real-time system metrics</small>
            </div>
            <div class="card-body">
                <div id="system-health-card">
                    <div class="loading-skeleton rounded" style="height: 200px;"></div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Recent Attendance Logs -->
    <div class="col-lg-8 mb-4">
        <div class="card h-100 animate__animated animate__fadeInUp" style="animation-delay: 0.7s">
            <div class="card-header d-flex justify-content-between align-items-center">
                <div>
                    <h5 class="card-title mb-0">
                        <i class="fas fa-clock me-2"></i>Recent Attendance Logs
                    </h5>
                    <small class="text-light opacity-75">Latest attendance records from the device</small>
                </div>
                <div class="btn-group" role="group">
                    <button class="btn btn-outline-light btn-sm" onclick="loadAttendanceLogs()" data-bs-toggle="tooltip" title="Refresh logs">
                        <i class="fas fa-sync-alt"></i>
                    </button>
                    <button class="btn btn-outline-light btn-sm" onclick="exportLogs()" data-bs-toggle="tooltip" title="Export logs">
                        <i class="fas fa-download"></i>
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div id="attendance-logs-card">
                    <div class="loading-skeleton rounded" style="height: 300px;"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Activity Timeline -->
    <div class="col-lg-4 mb-4">
        <div class="card h-100 animate__animated animate__fadeInUp" style="animation-delay: 0.8s">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-history me-2"></i>Activity Timeline
                </h5>
                <small class="text-light opacity-75">Recent system activities</small>
            </div>
            <div class="card-body">
                <div id="activity-timeline">
                    <div class="timeline">
                        <div class="timeline-item">
                            <div class="timeline-marker bg-success"></div>
                            <div class="timeline-content">
                                <h6 class="timeline-title">Module Initialized</h6>
                                <p class="timeline-text text-muted">EasyLink Yuswa module started successfully</p>
                                <small class="text-muted">Just now</small>
                            </div>
                        </div>
                        <div class="timeline-item">
                            <div class="timeline-marker bg-info"></div>
                            <div class="timeline-content">
                                <h6 class="timeline-title">Configuration Loaded</h6>
                                <p class="timeline-text text-muted">System configuration loaded from environment</p>
                                <small class="text-muted">2 minutes ago</small>
                            </div>
                        </div>
                        <div class="timeline-item">
                            <div class="timeline-marker bg-warning"></div>
                            <div class="timeline-content">
                                <h6 class="timeline-title">Device Check</h6>
                                <p class="timeline-text text-muted">Attempting to connect to device</p>
                                <small class="text-muted">5 minutes ago</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Test Results Modal -->
<div class="modal fade" id="testResultsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-vial me-2"></i>Module Test Results
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="test-results-content">
                    <!-- Test results will be loaded here -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" onclick="testModule()">Run Test Again</button>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('styles'); ?>
<style>
    /* Timeline Styles */
    .timeline {
        position: relative;
        padding-left: 30px;
    }

    .timeline::before {
        content: '';
        position: absolute;
        left: 15px;
        top: 0;
        bottom: 0;
        width: 2px;
        background: linear-gradient(to bottom, #667eea, #764ba2);
    }

    .timeline-item {
        position: relative;
        margin-bottom: 30px;
    }

    .timeline-marker {
        position: absolute;
        left: -22px;
        top: 0;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        border: 3px solid white;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }

    .timeline-content {
        background: rgba(255, 255, 255, 0.8);
        padding: 15px;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        border-left: 3px solid #667eea;
    }

    .timeline-title {
        margin-bottom: 5px;
        font-weight: 600;
        color: #333;
    }

    .timeline-text {
        margin-bottom: 5px;
        font-size: 0.9rem;
    }

    /* Enhanced Stats Cards */
    .stats-number {
        font-size: 2rem;
        font-weight: 700;
        background: var(--primary-gradient);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .stats-label {
        font-size: 0.9rem;
        color: #6c757d;
        font-weight: 500;
    }

    /* Connection Status Animations */
    .connection-pulse {
        animation: connectionPulse 2s infinite;
    }

    @keyframes connectionPulse {
        0% { transform: scale(1); opacity: 1; }
        50% { transform: scale(1.1); opacity: 0.7; }
        100% { transform: scale(1); opacity: 1; }
    }

    /* Enhanced Loading States */
    .loading-skeleton {
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
        background-size: 200% 100%;
        animation: loading 1.5s infinite;
        border-radius: 8px;
    }

    /* Hover Effects */
    .stats-card:hover {
        transform: translateY(-10px) scale(1.02);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    }

    .stats-card:hover .fa-3x {
        transform: scale(1.1) rotate(5deg);
        transition: all 0.3s ease;
    }
</style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<script>
// Dashboard Variables
let dashboardChart;
let refreshInterval;
let lastUpdateTime;

$(document).ready(function() {
    initializeDashboard();

    // Auto-refresh dashboard data every 60 seconds
    refreshInterval = setInterval(loadDashboardData, 60000);

    // Listen for global refresh events
    $(document).on('refresh-all', function() {
        loadDashboardData();
        loadAttendanceLogs();
    });
});

function initializeDashboard() {
    // Add loading animations
    $('.stats-card').each(function(index) {
        $(this).css('animation-delay', (index * 0.1) + 's');
    });

    // Load initial data
    loadDashboardData();
    loadAttendanceLogs();
    loadSystemHealth();

    // Initialize charts
    initializeCharts();
}

function loadDashboardData() {
    showLoading('#connection-status-card', 'Checking connection...');
    showLoading('#device-info-card', 'Loading device info...');
    showLoading('#stats-card', 'Loading statistics...');
    showLoading('#config-card', 'Loading configuration...');

    $.get('<?php echo e(route("easylinkyuswa.api.dashboard.data")); ?>')
        .done(function(response) {
            if (response.success) {
                updateConnectionStatus(response.data.connectivity);
                updateDeviceInfo(response.data.connectivity);
                updateStats(response.data.stats);
                updateConfiguration(response.data.config);
                updateLastRefreshTime();

                // Update progress bar
                $('#connection-progress').css('width', response.data.connectivity.connected ? '100%' : '0%');
            }
        })
        .fail(function(xhr) {
            handleApiError(xhr);
            showErrorStates();
        });
}

function showErrorStates() {
    $('#connection-status-card').html('<div class="text-danger"><i class="fas fa-exclamation-triangle"></i> Error loading data</div>');
    $('#device-info-card').html('<div class="text-danger"><i class="fas fa-exclamation-triangle"></i> Error loading data</div>');
    $('#stats-card').html('<div class="text-danger"><i class="fas fa-exclamation-triangle"></i> Error loading data</div>');
    $('#config-card').html('<div class="text-danger"><i class="fas fa-exclamation-triangle"></i> Error loading data</div>');
}

function updateConnectionStatus(connectivity) {
    const isConnected = connectivity.connected;
    const statusClass = isConnected ? 'success' : 'danger';
    const iconClass = isConnected ? 'check-circle' : 'times-circle';
    const statusText = isConnected ? 'Online' : 'Offline';

    const statusHtml = `
        <div class="text-center">
            <div class="mb-3">
                <i class="fas fa-${iconClass} fa-4x text-${statusClass} connection-pulse"></i>
            </div>
            <h4 class="stats-number">${statusText}</h4>
            <p class="stats-label mb-2">${isConnected ? 'Device Connected' : 'Connection Failed'}</p>
            ${isConnected ? `
                <div class="mb-2">
                    <span class="badge bg-success">
                        <i class="fas fa-clock me-1"></i>${formatResponseTime(connectivity.response_time)}
                    </span>
                </div>
            ` : ''}
            <small class="text-muted">
                <i class="fas fa-sync-alt me-1"></i>
                Last check: ${formatDateTime(connectivity.last_check)}
            </small>
        </div>
    `;

    $('#connection-status-card').html(statusHtml);

    // Update card border color
    $('#connection-status-card').closest('.stats-card')
        .removeClass('online offline')
        .addClass(isConnected ? 'online' : 'offline');
}

function updateDeviceInfo(connectivity) {
    const deviceHtml = `
        <div class="row g-3">
            <div class="col-12">
                <div class="d-flex align-items-center mb-2">
                    <i class="fas fa-server text-primary me-2"></i>
                    <strong>Host:</strong>
                    <span class="ms-auto text-muted">${connectivity.host}</span>
                </div>
            </div>
            <div class="col-12">
                <div class="d-flex align-items-center mb-2">
                    <i class="fas fa-fingerprint text-info me-2"></i>
                    <strong>Serial:</strong>
                    <span class="ms-auto text-muted">${connectivity.serial_number || 'N/A'}</span>
                </div>
            </div>
            <div class="col-12">
                <div class="d-flex align-items-center">
                    <i class="fas fa-signal text-${connectivity.connected ? 'success' : 'danger'} me-2"></i>
                    <strong>Status:</strong>
                    <span class="ms-auto">${getStatusBadge(connectivity.status, connectivity.connected)}</span>
                </div>
            </div>
        </div>
    `;

    $('#device-info-card').html(deviceHtml);
}

function updateStats(stats) {
    const statsHtml = `
        <div class="row g-3 text-center">
            <div class="col-6">
                <div class="stats-item">
                    <div class="stats-number">${stats.total_logs}</div>
                    <div class="stats-label">Total Logs</div>
                </div>
            </div>
            <div class="col-6">
                <div class="stats-item">
                    <div class="stats-number text-${stats.errors_count > 0 ? 'danger' : 'success'}">${stats.errors_count}</div>
                    <div class="stats-label">Errors</div>
                </div>
            </div>
            <div class="col-12 mt-3">
                <div class="d-flex justify-content-between align-items-center">
                    <span>Module Status:</span>
                    ${getStatusBadge('', stats.module_enabled)}
                </div>
            </div>
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <span>Device Status:</span>
                    ${getStatusBadge('', stats.device_connected)}
                </div>
            </div>
        </div>
    `;
    $('#stats-card').html(statsHtml);
}

function updateConfiguration(config) {
    const configHtml = `
        <div class="row g-3">
            <div class="col-md-6">
                <h6 class="text-gradient mb-3">
                    <i class="fas fa-cog me-2"></i>Module Settings
                </h6>
                <div class="config-item mb-2">
                    <strong>Name:</strong>
                    <span class="text-muted ms-2">${config.module_name}</span>
                </div>
                <div class="config-item mb-2">
                    <strong>Alias:</strong>
                    <span class="text-muted ms-2">${config.module_alias}</span>
                </div>
                <div class="config-item mb-2">
                    <strong>Debug Mode:</strong>
                    <span class="ms-2">${getStatusBadge('', config.debug)}</span>
                </div>
                <div class="config-item">
                    <strong>Auto Refresh:</strong>
                    <span class="ms-2">${getStatusBadge('', config.auto_refresh)}</span>
                </div>
            </div>
            <div class="col-md-6">
                <h6 class="text-gradient mb-3">
                    <i class="fas fa-network-wired me-2"></i>Device Settings
                </h6>
                <div class="config-item mb-2">
                    <strong>Host:</strong>
                    <span class="text-muted ms-2">${config.sdk_host}</span>
                </div>
                <div class="config-item mb-2">
                    <strong>Port:</strong>
                    <span class="text-muted ms-2">${config.server_port}</span>
                </div>
                <div class="config-item mb-2">
                    <strong>Timeout:</strong>
                    <span class="text-muted ms-2">${config.timeout}s</span>
                </div>
                <div class="config-item">
                    <strong>Refresh Interval:</strong>
                    <span class="text-muted ms-2">${config.refresh_interval}s</span>
                </div>
            </div>
        </div>
    `;
    $('#config-card').html(configHtml);
}

function loadAttendanceLogs() {
    showLoading('#attendance-logs-card', 'Loading attendance logs...');

    $.get('<?php echo e(route("easylinkyuswa.api.attendance.logs")); ?>')
        .done(function(response) {
            if (response.success && response.data.length > 0) {
                let logsHtml = `
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th><i class="fas fa-user me-1"></i>Employee</th>
                                    <th><i class="fas fa-calendar me-1"></i>Date</th>
                                    <th><i class="fas fa-clock me-1"></i>Time</th>
                                    <th><i class="fas fa-info-circle me-1"></i>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                `;

                response.data.slice(0, 8).forEach(function(log, index) {
                    const date = new Date(log.scan_date);
                    const employeeId = log.employee_id || log.nip || 'N/A';
                    const status = log.status || 'Unknown';

                    logsHtml += `
                        <tr class="animate__animated animate__fadeInUp" style="animation-delay: ${index * 0.1}s">
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="avatar-sm bg-primary rounded-circle d-flex align-items-center justify-content-center me-2">
                                        <i class="fas fa-user text-white"></i>
                                    </div>
                                    <strong>${employeeId}</strong>
                                </div>
                            </td>
                            <td>
                                <span class="text-muted">${date.toLocaleDateString()}</span>
                            </td>
                            <td>
                                <span class="badge bg-info">${date.toLocaleTimeString()}</span>
                            </td>
                            <td>
                                ${getStatusBadge(status)}
                            </td>
                        </tr>
                    `;
                });

                logsHtml += `
                            </tbody>
                        </table>
                    </div>
                    <div class="d-flex justify-content-between align-items-center mt-3">
                        <small class="text-muted">
                            <i class="fas fa-info-circle me-1"></i>
                            Showing latest 8 of ${formatNumber(response.count)} records
                        </small>
                        <button class="btn btn-outline-primary btn-sm" onclick="viewAllLogs()">
                            <i class="fas fa-eye me-1"></i>View All
                        </button>
                    </div>
                `;

                $('#attendance-logs-card').html(logsHtml);
            } else {
                $('#attendance-logs-card').html(`
                    <div class="text-center py-4">
                        <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                        <h6 class="text-muted">No attendance logs available</h6>
                        <p class="text-muted mb-3">No recent attendance data found from the device.</p>
                        <button class="btn btn-primary btn-sm" onclick="syncAttendance()">
                            <i class="fas fa-sync me-1"></i>Sync Now
                        </button>
                    </div>
                `);
            }
        })
        .fail(function(xhr) {
            $('#attendance-logs-card').html(`
                <div class="text-center py-4">
                    <i class="fas fa-exclamation-triangle fa-3x text-danger mb-3"></i>
                    <h6 class="text-danger">Failed to load attendance logs</h6>
                    <p class="text-muted mb-3">Unable to retrieve attendance data from the device.</p>
                    <button class="btn btn-outline-primary btn-sm" onclick="loadAttendanceLogs()">
                        <i class="fas fa-retry me-1"></i>Retry
                    </button>
                </div>
            `);
        });
}

function loadSystemHealth() {
    showLoading('#system-health-card', 'Loading system health...');

    $.get('<?php echo e(route("easylinkyuswa.api.stats")); ?>')
        .done(function(response) {
            if (response.success) {
                const stats = response.data;
                const healthScore = calculateHealthScore(stats);

                const healthHtml = `
                    <div class="text-center mb-3">
                        <div class="position-relative d-inline-block">
                            <canvas id="healthChart" width="120" height="120"></canvas>
                            <div class="position-absolute top-50 start-50 translate-middle">
                                <div class="stats-number">${healthScore}%</div>
                                <div class="stats-label">Health</div>
                            </div>
                        </div>
                    </div>
                    <div class="health-metrics">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span><i class="fas fa-plug text-success me-1"></i>Connection</span>
                            <span class="badge bg-${stats.device_connected ? 'success' : 'danger'}">
                                ${stats.device_connected ? 'Good' : 'Poor'}
                            </span>
                        </div>
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span><i class="fas fa-cog text-primary me-1"></i>Module</span>
                            <span class="badge bg-${stats.module_enabled ? 'success' : 'warning'}">
                                ${stats.module_enabled ? 'Active' : 'Inactive'}
                            </span>
                        </div>
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span><i class="fas fa-exclamation-triangle text-warning me-1"></i>Errors</span>
                            <span class="badge bg-${stats.errors_count === 0 ? 'success' : 'danger'}">
                                ${stats.errors_count}
                            </span>
                        </div>
                        <div class="d-flex justify-content-between align-items-center">
                            <span><i class="fas fa-database text-info me-1"></i>Data</span>
                            <span class="badge bg-info">${formatNumber(stats.total_logs)}</span>
                        </div>
                    </div>
                `;

                $('#system-health-card').html(healthHtml);

                // Initialize health chart
                setTimeout(() => initializeHealthChart(healthScore), 100);
            }
        })
        .fail(function() {
            $('#system-health-card').html(`
                <div class="text-center py-4">
                    <i class="fas fa-heartbeat fa-3x text-danger mb-3"></i>
                    <h6 class="text-danger">Health Check Failed</h6>
                    <p class="text-muted">Unable to retrieve system health data.</p>
                </div>
            `);
        });
}

// Utility Functions
function calculateHealthScore(stats) {
    let score = 0;
    if (stats.module_enabled) score += 25;
    if (stats.device_connected) score += 35;
    if (stats.errors_count === 0) score += 25;
    if (stats.total_logs > 0) score += 15;
    return Math.min(score, 100);
}

function initializeCharts() {
    // Initialize any charts here
}

function initializeHealthChart(score) {
    const ctx = document.getElementById('healthChart');
    if (!ctx) return;

    new Chart(ctx, {
        type: 'doughnut',
        data: {
            datasets: [{
                data: [score, 100 - score],
                backgroundColor: [
                    score >= 80 ? '#28a745' : score >= 60 ? '#ffc107' : '#dc3545',
                    '#e9ecef'
                ],
                borderWidth: 0
            }]
        },
        options: {
            responsive: false,
            maintainAspectRatio: false,
            cutout: '75%',
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    enabled: false
                }
            }
        }
    });
}

function updateLastRefreshTime() {
    lastUpdateTime = new Date();
    const timeString = lastUpdateTime.toLocaleTimeString();

    // Update any last refresh indicators
    $('.last-refresh').text(`Last updated: ${timeString}`);
}

function refreshDashboard() {
    if (isRefreshing) return;

    isRefreshing = true;
    showAlert('Refreshing dashboard...', 'info', 'Refresh');

    // Add visual feedback
    $('.stats-card').addClass('animate__animated animate__pulse');

    loadDashboardData();
    loadAttendanceLogs();
    loadSystemHealth();

    setTimeout(() => {
        isRefreshing = false;
        $('.stats-card').removeClass('animate__animated animate__pulse');
        showAlert('Dashboard refreshed successfully!', 'success');
    }, 2000);
}

function refreshConfig() {
    showLoading('#config-card', 'Refreshing configuration...');

    $.get('<?php echo e(route("easylinkyuswa.api.configuration")); ?>')
        .done(function(response) {
            if (response.success) {
                updateConfiguration(response.data);
                showAlert('Configuration refreshed!', 'success');
            }
        })
        .fail(function(xhr) {
            handleApiError(xhr);
        });
}

function testModule() {
    Swal.fire({
        title: 'Running Module Tests',
        html: `
            <div class="text-center">
                <div class="spinner-border text-primary mb-3" role="status"></div>
                <p>Testing module functionality...</p>
                <div class="progress">
                    <div class="progress-bar progress-bar-striped progress-bar-animated" style="width: 0%" id="test-progress"></div>
                </div>
            </div>
        `,
        showConfirmButton: false,
        allowOutsideClick: false,
        didOpen: () => {
            // Simulate progress
            let progress = 0;
            const interval = setInterval(() => {
                progress += 20;
                $('#test-progress').css('width', progress + '%');
                if (progress >= 100) {
                    clearInterval(interval);
                }
            }, 500);
        }
    });

    $.get('<?php echo e(route("easylinkyuswa.api.test.module")); ?>')
        .done(function(response) {
            const isSuccess = response.overall_test_result === 'passed';

            Swal.fire({
                title: isSuccess ? 'Tests Passed!' : 'Tests Failed',
                html: `
                    <div class="test-results">
                        <div class="alert alert-${isSuccess ? 'success' : 'danger'} mb-4">
                            <h6><i class="fas fa-${isSuccess ? 'check-circle' : 'times-circle'} me-2"></i>
                            Overall Result: ${response.overall_test_result.toUpperCase()}</h6>
                        </div>

                        <div class="row g-3">
                            <div class="col-md-4">
                                <div class="card border-${response.data.connectivity.connected ? 'success' : 'danger'}">
                                    <div class="card-body text-center">
                                        <i class="fas fa-wifi fa-2x text-${response.data.connectivity.connected ? 'success' : 'danger'} mb-2"></i>
                                        <h6>Connectivity</h6>
                                        <span class="badge bg-${response.data.connectivity.connected ? 'success' : 'danger'}">
                                            ${response.data.connectivity.connected ? 'PASS' : 'FAIL'}
                                        </span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card border-${response.data.device_info_status === 'success' ? 'success' : 'danger'}">
                                    <div class="card-body text-center">
                                        <i class="fas fa-microchip fa-2x text-${response.data.device_info_status === 'success' ? 'success' : 'danger'} mb-2"></i>
                                        <h6>Device Info</h6>
                                        <span class="badge bg-${response.data.device_info_status === 'success' ? 'success' : 'danger'}">
                                            ${response.data.device_info_status === 'success' ? 'PASS' : 'FAIL'}
                                        </span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card border-${response.data.attendance_logs_status === 'success' ? 'success' : 'danger'}">
                                    <div class="card-body text-center">
                                        <i class="fas fa-clock fa-2x text-${response.data.attendance_logs_status === 'success' ? 'success' : 'danger'} mb-2"></i>
                                        <h6>Attendance</h6>
                                        <span class="badge bg-${response.data.attendance_logs_status === 'success' ? 'success' : 'danger'}">
                                            ${response.data.attendance_logs_status === 'success' ? 'PASS' : 'FAIL'}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="mt-3">
                            <small class="text-muted">
                                <i class="fas fa-clock me-1"></i>
                                Test completed at: ${formatDateTime(response.test_timestamp)}
                            </small>
                        </div>
                    </div>
                `,
                icon: isSuccess ? 'success' : 'error',
                confirmButtonText: 'Close',
                confirmButtonClass: 'btn btn-primary',
                customClass: {
                    popup: 'swal-wide'
                }
            });
        })
        .fail(function(xhr) {
            Swal.fire({
                title: 'Test Failed',
                text: 'Unable to execute module tests. Please check your connection and try again.',
                icon: 'error',
                confirmButtonText: 'Close',
                confirmButtonClass: 'btn btn-danger'
            });
        });
}

function testDevice() {
    Swal.fire({
        title: 'Testing Device',
        text: 'Checking device connectivity and functionality...',
        icon: 'info',
        showConfirmButton: false,
        allowOutsideClick: false,
        didOpen: () => {
            Swal.showLoading();
        }
    });

    $.get('<?php echo e(route("easylinkyuswa.api.test.device")); ?>')
        .done(function(response) {
            if (response.success) {
                Swal.fire({
                    title: 'Device Test Passed!',
                    text: 'Device connectivity and functionality test completed successfully.',
                    icon: 'success',
                    confirmButtonText: 'Great!',
                    confirmButtonClass: 'btn btn-success'
                });
                loadDashboardData();
            } else {
                Swal.fire({
                    title: 'Device Test Failed',
                    text: response.message || 'Device test failed. Please check your device connection.',
                    icon: 'error',
                    confirmButtonText: 'OK',
                    confirmButtonClass: 'btn btn-danger'
                });
            }
        })
        .fail(function(xhr) {
            Swal.fire({
                title: 'Test Error',
                text: 'Unable to execute device test. Please try again.',
                icon: 'error',
                confirmButtonText: 'OK',
                confirmButtonClass: 'btn btn-danger'
            });
        });
}

function syncAttendance() {
    Swal.fire({
        title: 'Synchronizing Data',
        text: 'Retrieving attendance data from device...',
        icon: 'info',
        showConfirmButton: false,
        allowOutsideClick: false,
        didOpen: () => {
            Swal.showLoading();
        }
    });

    $.post('<?php echo e(route("easylinkyuswa.api.sync.attendance")); ?>')
        .done(function(response) {
            if (response.success) {
                Swal.fire({
                    title: 'Sync Successful!',
                    text: `Successfully synchronized ${response.data.count || 0} attendance records.`,
                    icon: 'success',
                    confirmButtonText: 'Excellent!',
                    confirmButtonClass: 'btn btn-success'
                });
                loadAttendanceLogs();
                loadDashboardData();
            } else {
                Swal.fire({
                    title: 'Sync Failed',
                    text: response.message || 'Failed to synchronize attendance data.',
                    icon: 'error',
                    confirmButtonText: 'OK',
                    confirmButtonClass: 'btn btn-danger'
                });
            }
        })
        .fail(function(xhr) {
            Swal.fire({
                title: 'Sync Error',
                text: 'Unable to synchronize data. Please check your connection.',
                icon: 'error',
                confirmButtonText: 'OK',
                confirmButtonClass: 'btn btn-danger'
            });
        });
}

function clearCache() {
    Swal.fire({
        title: 'Clear Cache?',
        text: 'This will clear all cached data and reload the configuration.',
        icon: 'question',
        showCancelButton: true,
        confirmButtonText: 'Yes, clear it!',
        cancelButtonText: 'Cancel',
        confirmButtonClass: 'btn btn-warning',
        cancelButtonClass: 'btn btn-secondary'
    }).then((result) => {
        if (result.isConfirmed) {
            Swal.fire({
                title: 'Clearing Cache',
                text: 'Please wait while we clear the cache...',
                icon: 'info',
                showConfirmButton: false,
                allowOutsideClick: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });

            $.post('<?php echo e(route("easylinkyuswa.api.cache.clear")); ?>')
                .done(function(response) {
                    if (response.success) {
                        Swal.fire({
                            title: 'Cache Cleared!',
                            text: 'Cache has been cleared successfully. The page will refresh.',
                            icon: 'success',
                            confirmButtonText: 'OK',
                            confirmButtonClass: 'btn btn-success'
                        }).then(() => {
                            location.reload();
                        });
                    } else {
                        Swal.fire({
                            title: 'Clear Failed',
                            text: response.message || 'Failed to clear cache.',
                            icon: 'error',
                            confirmButtonText: 'OK',
                            confirmButtonClass: 'btn btn-danger'
                        });
                    }
                })
                .fail(function(xhr) {
                    Swal.fire({
                        title: 'Clear Error',
                        text: 'Unable to clear cache. Please try again.',
                        icon: 'error',
                        confirmButtonText: 'OK',
                        confirmButtonClass: 'btn btn-danger'
                    });
                });
        }
    });
}

// Additional utility functions
function exportData() {
    $.get('<?php echo e(route("easylinkyuswa.api.config.export")); ?>')
        .done(function(response) {
            if (response.success) {
                const dataStr = JSON.stringify(response.data, null, 2);
                const dataBlob = new Blob([dataStr], {type: 'application/json'});
                const url = URL.createObjectURL(dataBlob);
                const link = document.createElement('a');
                link.href = url;
                link.download = response.filename || 'easylinkyuswa_config.json';
                link.click();
                showAlert('Configuration exported successfully!', 'success');
            }
        })
        .fail(function(xhr) {
            showAlert('Failed to export configuration', 'error');
        });
}

function exportLogs() {
    showAlert('Export functionality coming soon!', 'info');
}

function viewAllLogs() {
    showAlert('View all logs functionality coming soon!', 'info');
}

// Custom CSS for SweetAlert
const style = document.createElement('style');
style.textContent = `
    .swal-wide {
        width: 800px !important;
    }
    .swal2-popup {
        border-radius: 15px !important;
    }
`;
document.head.appendChild(style);
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('easylinkyuswa::layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\laragon\www\larattendance\modules\EasyLinkYuswa\Providers/../Resources/views/dashboard.blade.php ENDPATH**/ ?>